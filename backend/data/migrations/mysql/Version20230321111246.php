<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230321111246 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn('min_score', 'integer', [
            'notnull' => false,
        ]);
        $table->addColumn('max_score', 'integer', [
            'notnull' => false,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('min_score');
        $table->dropColumn('max_score');
    }

}
