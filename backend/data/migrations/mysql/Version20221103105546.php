<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221103105546 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('events_algo_events');
        $table->addColumn('event_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('algo_event', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->addForeignKeyConstraint('events', ['event_id'], ['event_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey([
            'event_id',
            'algo_event',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('events_algo_events');
    }

}
