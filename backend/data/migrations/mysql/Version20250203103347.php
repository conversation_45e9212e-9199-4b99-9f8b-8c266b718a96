<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250203103347 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');

        $table->modifyColumn('good_performance_example', [
            'notnull' => false,
        ]);
        $table->modifyColumn('bad_performance_example', [
            'notnull' => false,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists_points');

        $table->modifyColumn('good_performance_example', [
            'notnull' => true,
        ]);
        $table->modifyColumn('bad_performance_example', [
            'notnull' => true,
        ]);
    }

}
