<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418053845 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                permissions
            SET
                permission_name = "Flows",
                system_name = "flows"            
            WHERE
                permission_id = 33
        ');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                permissions
            SET
                permission_name = "Checklists",
                system_name = "checklists"
            WHERE
                permission_id = 33
        ');
    }
}
