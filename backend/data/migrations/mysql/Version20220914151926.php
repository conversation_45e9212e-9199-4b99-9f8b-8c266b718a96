<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220914151926 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('companies');
        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('company_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('lower_threshold_bar', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 30,
        ]);
        $table->addColumn('upper_threshold_bar', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 70,
        ]);
        $table->addColumn('company_avatar', 'text', [
            'notnull' => false,
            'length' => 65535,
        ]);
        $table->addColumn('deleted', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);

        $table->setPrimaryKey([
            'company_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('companies');
    }

}
