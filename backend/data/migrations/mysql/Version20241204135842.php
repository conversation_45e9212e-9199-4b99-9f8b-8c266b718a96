<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241204135842 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('algo_apis_industries');

        $table->addColumn('algo_api_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('algo_apis', ['algo_api_id'], ['algo_api_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addColumn('industry_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('industries', ['industry_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('algo_apis_industries');
    }
}
