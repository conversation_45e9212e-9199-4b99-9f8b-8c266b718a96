<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221007124510 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                api_applications
            ADD COLUMN
                company_id BIGINT UNSIGNED DEFAULT NULL
            AFTER
                application_type;
        ');
        
        $table = $schema->getTable('api_applications');
        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], 'FK_FDA3BF5D979B1AD6');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('api_applications');
        $table->removeForeignKey('FK_FDA3BF5D979B1AD6');
        $table->dropColumn('company_id');
    }

}
