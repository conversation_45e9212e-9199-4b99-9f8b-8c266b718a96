<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230406101231 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $result = $this->connection->executeQuery('
            SELECT
                role_id
            FROM 
                roles 
            WHERE
                role_name LIKE "%qc%"
        ');
        foreach ($result->fetchFirstColumn() as $roleId) {
            $insertRecords = [];
            $values = [
                'id',
                'name',
                'call_duration',
                'call_reviewed',
                'calls_attempts',
                'category_bar',
                'category_values',
                'score',
            ];
            foreach ($values as $key => $value) {
                $insertRecords[] = '(' . $roleId . ', "calls", "' . $value . '", ' . $key + 1 . ')';
            }
            $sql = '
                INSERT INTO
                    roles_displayed_columns
                    (
                        role_id,
                        report,
                        `column`,
                        sort
                    )
                VALUES
            ' . implode(',', $insertRecords);
            $this->connection->executeQuery($sql);
        }
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            TRUNCATE TABLE roles_displayed_columns;
        ');
    }

}
