<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530132807 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function up(Schema $schema): void
    {
        $today = date('Y-m-d H:i:s');

        $this->connection->executeQuery(
            '
            UPDATE
                companies
            SET
                created_at = "' . $today . '"
        '
        );

        $table = $schema->getTable('companies');
        $table->modifyColumn('created_at', ['notnull' => true]);
    }

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->modifyColumn('created_at', ['notnull' => false]);
    }
}
