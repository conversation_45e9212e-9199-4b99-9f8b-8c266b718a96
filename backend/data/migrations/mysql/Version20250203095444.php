<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250203095444 extends AbstractMigration {

    const string UNIQUE_INDEX_NAME = 'checklists_role_id_unqi';

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('checklists');
        $table->addUniqueIndex([
            'role_id',
        ], self::UNIQUE_INDEX_NAME);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('checklists');
        $table->dropIndex(self::UNIQUE_INDEX_NAME);
    }
}
