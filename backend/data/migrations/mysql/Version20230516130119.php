<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230516130119 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                events_categories
            MODIFY 
                `color_id` 
                SMALLINT(5)
                UNSIGNED 
                NULL
        ');
        $table = $schema->getTable('events_categories');
        $table->addForeignKeyConstraint('events_colors', ['color_id'], ['color_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'SET NULL',
        ], 'FK_EF0AF3E97ADA1FB5');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                events_categories
            SET
                color_id = 1
            WHERE color_id IS NULL
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                events_categories
            DROP FOREIGN KEY 
                FK_EF0AF3E97ADA1FB5,
            MODIFY 
                `color_id` 
                SMALLINT(5)
                UNSIGNED 
                NOT NULL;
        ');
        $table = $schema->getTable('events_categories');
        $table->addForeignKeyConstraint('events_colors', ['color_id'], ['color_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], 'FK_EF0AF3E97ADA1FB5');
    }

}
