<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528115526 extends AbstractMigration
{

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn(
            'status',
            'string',
            [
                'notnull' => true,
                'default' => 'active',
                'length' => 20,
            ]
        );
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('status');
    }
}
