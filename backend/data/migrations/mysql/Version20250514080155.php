<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514080155 extends AbstractMigration
{

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn('llm_events_limit', 'integer', [
            'notnull' => false,
            'unsigned' => true,
            'default' => 0,
        ]);
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('llm_events_limit');
    }
}
