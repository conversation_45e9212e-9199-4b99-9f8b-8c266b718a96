<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220923084031 extends AbstractMigration
{
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('roles_permissions');
        $table->addColumn('access_level', 'string', [
            'notnull' => true,
            'default' => 'read',
            'length' => 15,
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('roles_permissions');
        $table->dropColumn('access_level');
    }
}
