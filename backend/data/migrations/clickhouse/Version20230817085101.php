<?php

namespace Clickhouse\Migrations;

class Version20230817085101 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_emotional_event_analyzed Bool DEFAULT 0
            AFTER
                is_analyzed
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                is_emotional_event_analyzed
        ');
    }
}
