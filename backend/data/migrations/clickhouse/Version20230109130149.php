<?php

namespace Clickhouse\Migrations;

class Version20230109130149 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                event_happenings_changes
            ADD COLUMN
                role_id UInt32
            AFTER paragraph_number
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                event_happenings_changes
            DROP COLUMN
                role_id
        ');
    }
}
