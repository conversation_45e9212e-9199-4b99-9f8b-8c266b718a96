<?php

namespace Clickhouse\Migrations;

class Version20230417125243 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            <PERSON><PERSON><PERSON> TABLE
                calls
            DROP COLUMN
                client_name
        ');
        $this->getClient()->write('
            <PERSON>TE<PERSON> TABLE
                calls
            DROP COLUMN
                client_status
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                client_value
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                client_source
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            <PERSON>TE<PERSON> TABLE
                calls
            ADD COLUMN
                client_source Nullable(String) DEFAULT NULL
            AFTER
                client_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                client_value Nullable(Float32) DEFAULT NULL
            AFTER
                client_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                client_status Nullable(String) DEFAULT NULL
            AFTER
                client_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                client_name String DEFAULT \'\'
            AFTER
                client_id
        ');
    }
}
