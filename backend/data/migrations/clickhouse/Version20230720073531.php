<?php

namespace Clickhouse\Migrations;

class Version20230720073531 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                reviewer_user_id Nullable(UInt64) DEFAULT NULL
            AFTER
                reviewed_time
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                reviewer_user_id
        ');
    }
}
