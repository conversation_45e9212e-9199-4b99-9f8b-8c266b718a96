<?php

namespace Clickhouse\Migrations;

class Version20240129102508 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                is_fully_reviewed Bool DEFAULT 0
            AFTER is_reviewed
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                is_partly_reviewed Bool DEFAULT 0
            AFTER is_reviewed
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            ADD COLUMN
                event_changed_from_event_id Nullable(UInt64) DEFAULT NULL
            AFTER paragraph_start_time
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                is_fully_reviewed
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls
            DROP COLUMN
                is_partly_reviewed
        ');
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            DROP COLUMN
                event_changed_from_event_id
        ');
    }
}
