<?php

namespace Clickhouse\Migrations;

class Version20221229134512 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE event_happenings_changes_log (
                company_id UInt32,
                highlighted_text String,
                fragment_text String,
                original_status String,
                corrected_status String,
                created DateTime DEFAULT now()
            )
            ENGINE = Log
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE event_happenings_changes_log
        ');
    }
}
