<?php

namespace Clickhouse\Migrations;

class Version20230106140021 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                score Float32 DEFAULT 0
            AFTER original_file_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                reviewed UInt8 DEFAULT 0
            AFTER score
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                score
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                reviewed
        ');
    }
}
