<?php

namespace Clickhouse\Migrations;

class Version20230309141254 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE
                event_happenings_changes_log
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            CREATE TABLE event_happenings_changes_log (
                company_id UInt32,
                role_id UInt32,
                highlighted_text String,
                fragment_text String,
                original_status String,
                is_correct UInt8,
                created DateTime DEFAULT now()
            )
            ENGINE = Log
        ');
    }
}
