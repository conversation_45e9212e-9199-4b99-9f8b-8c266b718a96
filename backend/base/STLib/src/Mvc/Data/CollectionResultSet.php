<?php

namespace STLib\Mvc\Data;

use Laminas\Db\ResultSet\ResultSet;
use RuntimeException;
use STLib\Expand\Collection;

class CollectionResultSet extends ResultSet
{
    private string $collectionClassName;

    public function setCollectionClassName(string $collectionClassName): void
    {
        $collection = new $collectionClassName();
        if (!is_a($collection, Collection::class)) {
            throw new RuntimeException(
                sprintf(
                    'Collection class name must be an instance of %s, %s given.',
                    Collection::class,
                    $collectionClassName
                )
            );
        }

        $this->collectionClassName = $collectionClassName;
    }

    public function getCollection(): Collection
    {
        $collection = new $this->collectionClassName();
        foreach ($this as $item) {
            $collection->add($item);
        }

        return $collection;
    }
}
