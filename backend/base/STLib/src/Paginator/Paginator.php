<?php

namespace STLib\Paginator;

class Paginator extends \Laminas\Paginator\Paginator {
    
    /**
     *
     * @var string
     */
    private string $sortColumn = 'id';

    /**
     *
     * @var array
     */
    private $filterColumns = [];
    
    /**
     *
     * @var string
     */
    private $sortDirection = 'asc';
    
    /**
     *
     * @var array
     */
    private $items = [];
    
    /**
     * 
     * @param string $sortColumn
     * @return Paginator
     */
    public function setSortColumn(string $sortColumn): Paginator {
        $this->sortColumn = $sortColumn;
        return $this;
    }
    
    /**
     * 
     * @return string
     */
    public function getSortColumn(): string {
        return $this->sortColumn;
    }
    
    /**
     * 
     * @param string $sortDirection
     * @return Paginator
     */
    public function setSortDirection(string $sortDirection): Paginator {
        if (strtoupper($sortDirection) !== \Laminas\Db\Sql\Select::ORDER_ASCENDING && strtoupper($sortDirection) !== \Laminas\Db\Sql\Select::ORDER_DESCENDING) {
            throw new \InvalidArgumentException('Invalid sort direction');
        }
        $this->sortDirection = strtolower($sortDirection);
        return $this;
    }
    
    /**
     * 
     * @return string
     */
    public function getSortDirection(): string {
        return $this->sortDirection;
    }
    
    /**
     * 
     * @param array $items
     * @return Paginator
     */
    public function setItems(\STLib\Expand\Collection|array $items): Paginator {
        $this->items = $items;
        return $this;
    }
    
    /**
     * 
     * @param mixed $item
     * @return mixed
     */
    public function addItem(mixed $item): mixed {
        $this->items[] = $item;
        return $item;
    }
    
    /**
     * 
     * @return bool
     */
    public function hasItems(): bool {
        return count($this->items) > 0;
    }

    /**
     * 
     * @return array
     */
    public function getItems(): \STLib\Expand\Collection|array {
        return $this->items;
    }
    
    /**
     * 
     * @return Paginator
     */
    public function clearItems(): Paginator {
        $this->items = [];
        return $this;
    }
    
    /**
     * 
     * @param string $sortColumn
     * @return bool
     */
    public function isActiveSortColumn(string $sortColumn): bool {
        return $sortColumn === $this->getSortColumn();
    }

    /**
     *
     * @param array $filters
     * @return Paginator
     */
    public function setFilterValues(array $filters): Paginator {
        $this->filterColumns = $filters;
        return $this;
    }

    /**
     * @return array
     */
    public function getFilterValues(): array {
        return $this->filterColumns;
    }

    /**
     * 
     * @param string $column
     * @param string $format
     * @return string|null
     */
    public function getFilterValue(string $column, string $format = 'd.m.Y'): ?string {
        if (!(array_key_exists($column, $this->filterColumns))) {
            return null;
        }
        if ($this->filterColumns[$column] instanceof \DateTime) {
            return $this->filterColumns[$column]->format($format);
        }
        return $this->filterColumns[$column];
    }
    
    /**
     * 
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = null): array {
        return [
            'count' => $this->getTotalItemCount(),
            'result' => $this->getItems()->toArray($attributes),
            'last_page' => $this->getPages()->last,
            'page' => $this->getCurrentPageNumber(),
        ];
    }
}
