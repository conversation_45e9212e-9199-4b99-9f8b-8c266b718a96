<?php

declare(strict_types=1);

namespace STEms\Entity;

use Carbon\Carbon;

class DataSetExampleEvent
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     * @var string
     */
    public string $dataSetExampleEventId;

    /**
     * @var string
     */
    public string $dataSetExampleId;

    /**
     * @var int
     */
    public int $eventId;

    /**
     * @var ?string
     */
    public ?string $eventIcon = null;

    /**
     * @var ?string
     */
    public ?string $eventName = null;

    /**
     * @var ?string
     */
    public ?string $eventFillColorHex = null;

    /**
     * @var ?string
     */
    public ?string $eventOutlineColorHex = null;

    /**
     * @var string
     */
    public string $highlight;

    /**
     * @var string
     */
    public string $enHighlight;

    /**
     * @var bool
     */
    public bool $isDeleted = false;

    /**
     * @var Carbon|null
     */
    protected ?\Carbon\Carbon $createdAt = null;

    /**
     * @var Carbon|null
     */
    protected ?\Carbon\Carbon $updatedAt = null;

    /**
     * @return string
     */
    public function getDataSetExampleId(): string
    {
        return $this->dataSetExampleId;
    }

    /**
     * @param string $dataSetExampleId
     * @return DataSetExampleEvent
     */
    public function setDataSetExampleId(string $dataSetExampleId): DataSetExampleEvent
    {
        $this->dataSetExampleId = $dataSetExampleId;

        return $this;
    }

    /**
     * @return string
     */
    public function getDataSetExampleEventId(): string
    {
        return $this->dataSetExampleEventId;
    }

    /**
     * @param string $dataSetExampleId
     * @return DataSetExampleEvent
     */
    public function setDataSetExampleEventId(string $dataSetExampleEventId): DataSetExampleEvent
    {
        $this->dataSetExampleEventId = $dataSetExampleEventId;

        return $this;
    }

    /**
     * @return ?string
     */
    public function getHighlight(): ?string
    {
        return $this->highlight;
    }

    /**
     * @param ?string $highlight
     * @return DataSetExampleEvent
     */
    public function setHighlight(?string $highlight): DataSetExampleEvent
    {
        $this->highlight = $highlight;

        return $this;
    }

    /**
     * @return ?string
     */
    public function getEnHighlight(): ?string
    {
        return $this->enHighlight;
    }

    /**
     * @param string|null $enHighlight
     * @return DataSetExampleEvent
     */
    public function setEnHighlight(?string $enHighlight): DataSetExampleEvent
    {
        $this->enHighlight = $enHighlight;

        return $this;
    }

    /**
     * @return int
     */
    public function getEventId(): int
    {
        return $this->eventId;
    }

    /**
     * @param int $eventId
     * @return DataSetExampleEvent
     */
    public function setEventId(int $eventId): DataSetExampleEvent
    {
        $this->eventId = $eventId;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getEventIcon(): ?string
    {
        return $this->eventIcon;
    }

    /**
     * @param string $eventIcon
     * @return DataSetExampleEvent
     */
    public function setEventIcon(string $eventIcon): DataSetExampleEvent
    {
        $this->eventIcon = $eventIcon;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getEventFillColorHex(): ?string
    {
        return $this->eventFillColorHex;
    }

    /**
     * @param string $eventFillColorHex
     * @return DataSetExampleEvent
     */
    public function setEventFillColorHex(string $eventFillColorHex): DataSetExampleEvent
    {
        $this->eventFillColorHex = $eventFillColorHex;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getEventOutlineColorHex(): ?string
    {
        return $this->eventOutlineColorHex;
    }

    /**
     * @param string $eventOutlineColorHex
     * @return DataSetExampleEvent
     */
    public function setEventOutlineColorHex(string $eventOutlineColorHex): DataSetExampleEvent
    {
        $this->eventOutlineColorHex = $eventOutlineColorHex;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getEventName(): ?string
    {
        return $this->eventName;
    }

    /**
     * @param string $eventName
     * @return DataSetExampleEvent
     */
    public function setEventName(string $eventName): DataSetExampleEvent
    {
        $this->eventName = $eventName;
        return $this;
    }

    /**
     * @param bool|null $isDeleted
     * @return bool|DataSetExampleEvent
     */
    public function isDeleted(bool $isDeleted = null): bool|DataSetExampleEvent
    {
        if (is_null($isDeleted)) {
            return $this->isDeleted;
        }
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     * @param bool $isDeleted
     * @return DataSetExampleEvent
     */
    public function setIsDeleted(bool $isDeleted): DataSetExampleEvent
    {
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     * @return bool
     */
    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     * @return Carbon|null
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    /**
     * @param string|Carbon $createdAt
     * @return DataSetExampleEvent
     */
    public function setCreatedAt(string|\Carbon\Carbon $createdAt): self
    {
        $this->createdAt = is_string($createdAt) ? \Carbon\Carbon::parse($createdAt) : $createdAt;

        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updatedAt;
    }

    /**
     * @param string|Carbon $updatedAt
     * @return DataSetExampleEvent
     */
    public function setUpdatedAt(string|\Carbon\Carbon $updatedAt): self
    {
        $this->updatedAt = is_string($updatedAt) ? \Carbon\Carbon::parse($updatedAt) : $updatedAt;

        return $this;
    }

    /**
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = $this->extract($this);

        if (empty($attributes)) {
            return $result;
        }

        return array_filter($result, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
