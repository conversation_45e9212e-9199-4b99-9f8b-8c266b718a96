<?php

declare(strict_types=1);

namespace STEms\Entity;

use Carbon\Carbon;
use STEms\Data\EmsDataSetExamplesTable;

class DataSetExample
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const EXAMPLE_SOURCE_CONFIRMED = 'confirmed';
    public const EXAMPLE_SOURCE_NEUTRAL = 'neutral';
    public const EXAMPLE_SOURCE_CUSTOM = 'custom';
    public const EXAMPLE_SOURCE_ANALYZED_CALLS_NEUTRAL = 'analyzed_calls_neutral';
    public const EXAMPLE_SOURCE_ANALYZED_CALLS_CONFIRMED = 'analyzed_calls_confirmed';

    /**
     * @var string
     */
    public string $dataSetExampleId;

    /**
     * @var string
     */
    public string $dataSetId;

    /**
     * @var string
     */
    public string $text;

    /**
     * @var ?string
     */
    public ?string $enText = null;

    /**
     * @var string|null
     */
    public ?string $language = null;

    /**
     * @var ?string
     */
    public ?string $callId = null;

    /**
     * @var ?int
     */
    public ?int $paragraphNumber = null;

    /**
     * @var ?int
     */
    public ?int $paragraphStartTime = null;

    /**
     * @var string
     */
    public string $status = EmsDataSetExamplesTable::STATUS_UNSORTED;

    /**
     * @var string
     */
    public string $exampleSource = self::EXAMPLE_SOURCE_CONFIRMED;

    /**
     *
     * @var DataSetExampleEventCollection|null
     */
    protected ?DataSetExampleEventCollection $events = null;

    /**
     * @var bool
     */
    public bool $isDeleted = false;

    /**
     * @var Carbon|null
     */
    protected ?\Carbon\Carbon $createdAt = null;

    /**
     * @var Carbon|null
     */
    protected ?\Carbon\Carbon $updatedAt = null;

    /**
     * @return string
     */
    public function getDataSetExampleId(): string
    {
        return $this->dataSetExampleId;
    }

    /**
     * @param string $dataSetExampleId
     * @return DataSetExample
     */
    public function setDataSetExampleId(string $dataSetExampleId): DataSetExample
    {
        $this->dataSetExampleId = $dataSetExampleId;

        return $this;
    }

    /**
     * @return string
     */
    public function getDataSetId(): string
    {
        return $this->dataSetId;
    }

    /**
     * @param string $dataSetId
     * @return DataSetExample
     */
    public function setDataSetId(string $dataSetId): DataSetExample
    {
        $this->dataSetId = $dataSetId;

        return $this;
    }

    /**
     * @return string
     */
    public function getText(): string
    {
        return $this->text;
    }

    /**
     * @param string $text
     * @return DataSetExample
     */
    public function setText(string $text): DataSetExample
    {
        $this->text = $text;

        return $this;
    }

    /**
     * @return ?string
     */
    public function getEnText(): ?string
    {
        return $this->enText;
    }

    /**
     * @param string|null $enText
     * @return DataSetExample
     */
    public function setEnText(?string $enText): DataSetExample
    {
        $this->enText = $enText;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getLanguage(): ?string
    {
        return $this->language;
    }

    /**
     * @param string|null $language
     * @return DataSetExample
     */
    public function setLanguage(?string $language): DataSetExample
    {
        $this->language = $language;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getCallId(): ?string
    {
        return $this->callId;
    }

    /**
     * @param string|null $callId
     * @return DataSetExample
     */
    public function setCallId(?string $callId): DataSetExample
    {
        $this->callId = $callId;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getParagraphNumber(): ?int
    {
        return $this->paragraphNumber;
    }

    /**
     * @param int|null $paragraphNumber
     * @return DataSetExample
     */
    public function setParagraphNumber(?int $paragraphNumber): DataSetExample
    {
        $this->paragraphNumber = $paragraphNumber;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getParagraphStartTime(): ?int
    {
        return $this->paragraphStartTime;
    }

    /**
     * @param int|null $paragraphStartTime
     * @return DataSetExample
     */
    public function setParagraphStartTime(?int $paragraphStartTime): DataSetExample
    {
        $this->paragraphStartTime = $paragraphStartTime;

        return $this;
    }

    /**
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return DataSetExample
     */
    public function setStatus(string $status): DataSetExample
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return string
     */
    public function getExampleSource(): string
    {
        return $this->exampleSource;
    }

    /**
     * @param string $exampleSource
     * @return DataSetExample
     */
    public function setExampleSource(string $exampleSource): DataSetExample
    {
        $this->exampleSource = $exampleSource;

        return $this;
    }

    /**
     * @return bool
     */
    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     * @param bool $isDeleted
     * @return DataSetExample
     */
    public function setIsDeleted(bool $isDeleted): DataSetExample
    {
        $this->isDeleted = $isDeleted;

        return $this;
    }

    /**
     * @param bool|null $isDeleted
     * @return bool|DataSetExample
     */
    public function isDeleted(bool $isDeleted = null): bool|DataSetExample
    {
        if (is_null($isDeleted)) {
            return $this->isDeleted;
        }
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     * @return ?DataSetExampleEventCollection
     */
    public function getEvents(): ?DataSetExampleEventCollection
    {
        return $this->events;
    }

    /**
     * @param DataSetExampleEventCollection|null $events
     * @return DataSetExample
     */
    public function setEvents(?DataSetExampleEventCollection $events): DataSetExample
    {
        $this->events = $events;
        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    /**
     * @param string|Carbon $createdAt
     * @return DataSetExample
     */
    public function setCreatedAt(string|\Carbon\Carbon $createdAt): self
    {
        $this->createdAt = is_string($createdAt) ? \Carbon\Carbon::parse($createdAt) : $createdAt;

        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updatedAt;
    }

    /**
     * @param string|Carbon $updatedAt
     * @return DataSetExample
     */
    public function setUpdatedAt(string|\Carbon\Carbon $updatedAt): self
    {
        $this->updatedAt = is_string($updatedAt) ? \Carbon\Carbon::parse($updatedAt) : $updatedAt;

        return $this;
    }

    /**
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = $this->extract($this);

        if ($this->getEvents() instanceof DataSetExampleEventCollection) {
            $result['events'] = $this->getEvents()->toArray();
        } else {
            unset($result['events']);
        }

        if (empty($attributes)) {
            return $result;
        }

        return array_filter($result, function ($attribute) use ($attributes) {
            return in_array($attribute, $attributes);
        }, ARRAY_FILTER_USE_KEY);
    }
}
