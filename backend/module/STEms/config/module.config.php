<?php

declare(strict_types=1);

namespace STEms;

return [
    'controller_plugins' => [
        'invokables' => [
            'ems' => Controller\Plugin\Ems::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\EmsDataSetExamplesTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\EmsDataSetExamplesEventsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\EmsDataSetsTable::class => \STClickhouse\Entity\TableFactory::class,
            Service\EmsDataSetService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\EmsDataSetExampleService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Service\EmsEventsService::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Validator\DataSetValidator::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Validator\DataSetExampleValidator::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Validator\DataSetExampleEventValidator::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Validator\DataSetExamplesUploadValidator::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
            Validator\DataSetExamplesSearchValidator::class => \STLib\Mvc\DependencyInjection\DefaultFactory::class,
        ],
    ],
];
