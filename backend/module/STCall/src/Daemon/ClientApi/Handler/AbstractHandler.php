<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi\Handler;

use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;
use ST<PERSON>ib\Mvc\Hydrator\BaseHydratorTrait;
use STRoboTruck\Service\DataCollection\DataCollector;

abstract class AbstractHandler
{
    use BaseHydratorTrait;

    /**
     *
     * @var UploadService
     */
    protected UploadService $uploadService;

    /**
     *
     * @var CallAnalysisService
     */
    protected CallAnalysisService $callAnalysisService;

    /**
     *
     * @var Company
     */
    protected Company $company;

    /**
     *
     * @var string
     */
    protected string $requestId;

    /**
     *
     * @param UploadService $uploadService
     * @param CallAnalysisService $callAnalysisService
     * @param Company $company
     * @param string $requestId
     */
    public function __construct(
        protected readonly FeaturesAvailableCheckerInterface $featuresAvailableChecker,
        protected readonly DataCollector $dataCollector,
        UploadService $uploadService,
        CallAnalysisService $callAnalysisService,
        Company $company,
        string $requestId,
    ) {
        $this->uploadService = $uploadService;
        $this->callAnalysisService = $callAnalysisService;
        $this->company = $company;
        $this->requestId = $requestId;
    }

    /**
     *
     * @param array $requestBody
     * @return bool
     */
    abstract public function run(array $requestBody): bool;
}
