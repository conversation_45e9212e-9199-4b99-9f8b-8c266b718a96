<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi\Handler;

use Exception;
use STCall\Service\CallAnalysis\LanguageDetectionStep;
use STCall\Service\CallAnalysis\TranslationStep;
use STCall\Service\Import\UploadParams\UploadParams;
use STRoboTruck\Service\DataCollection\DataCollector;

class ChatCallHandler extends AbstractHandler
{
    /**
     *
     * @param array $requestBody
     * @return bool
     * @throws Exception
     */
    public function run(array $requestBody): bool
    {
        if (!$this->featuresAvailableChecker->isChatAnalysisAvailable($this->company)) {
            $this->dataCollector->collect(
                DataCollector::EVENT_CALL_ANALYSIS_NOT_AVAILABLE,
                json_encode($requestBody),
                [
                    'id' => $this->requestId,
                    'company_id' => $this->company->getId(),
                    'call_id' => null,
                ]
            );

            return false;
        }

        /** @var UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 'chat-api-upload',
                'company' => $this->company,
                'options' => $requestBody,
            ],
            UploadParams::class
        );
        $result = $this->uploadService->uploadCall($uploadParams);

        $nextStep = match ($result->isUpdate()) {
            true => TranslationStep::CALL_TRANSLATION_QUEUE,
            default => LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE,
        };
        $this->callAnalysisService->addToQueue(
            $this->company->getId(),
            $result->getCall(),
            $nextStep,
            '',
            [
                'request_id' => $this->requestId,
            ]
        );

        return true;
    }
}
