<?php

declare(strict_types=1);

namespace STCall\Daemon\ClientApi;

use Exception;
use ST<PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Daemon\ClientApi\Handler\AbstractHandler;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadService;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;
use STCompany\Service\CompanyService;
use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;

class ClientApiRequestDaemon extends AbstractDaemon
{
    public const string QUEUE_NAME = 'client-api-request';
    public const string ERROR_QUEUE_NAME = 'client-api-request-error';

    /**
     *
     * @var Company|null
     */
    protected ?Company $company = null;

    /**
     *
     * @var string
     */
    protected string $requestId = '';

    public function __construct(
        private readonly CompanyService $companyService,
        private readonly FeaturesAvailableCheckerInterface $featuresAvailable<PERSON>hecker,
        private readonly DataCollector $dataCollector,
    ) {
    }

    /**
     *
     * @param string $message
     * @return void
     * @throws NotFoundApiException
     * @throws Exception
     */
    public function handle(string $message): void
    {
        $data = json_decode($message);
        $requestBody = json_decode($data->request_body, true) ?? [];

        try {
            $this->company = $this->companyService->getCompanyByApiToken($data->company_bearer_token);
        } catch (NotFoundApiException $e) {
            $this->doNotMakeNextAttempt(true);
            throw $e;
        }
        $this->requestId = $data->request_id;

        $this->log($requestBody);

        $handler = $this->getHandler($requestBody);
        $handler->run($requestBody);
    }

    /**
     *
     * @param array $data
     * @return AbstractHandler
     */
    protected function getHandler(array $data): AbstractHandler
    {
        $uploadService = $this->params()->offsetGet(UploadService::class);
        $callAnalysisService = $this->params()->offsetGet(CallAnalysisService::class);
        if (isset($data['chat_call'])) {
            return new Handler\ChatCallHandler(
                $this->featuresAvailableChecker,
                $this->dataCollector,
                $uploadService,
                $callAnalysisService,
                $this->company,
                $this->requestId
            );
        }
        return new Handler\CallHandler(
            $this->featuresAvailableChecker,
            $this->dataCollector,
            $uploadService,
            $callAnalysisService,
            $this->company,
            $this->requestId
        );
    }

    private function log(array $requestBody): void
    {
        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_UPLOAD_API_REQUEST,
            json_encode($requestBody),
            [
                'id' => $this->requestId,
                'company_id' => $this->company->getId(),
                'call_id' => null,
            ],
            'api-calls-logs'
        );
    }
}
