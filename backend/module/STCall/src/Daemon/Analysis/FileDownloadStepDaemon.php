<?php

declare(strict_types=1);

namespace STCall\Daemon\Analysis;

use STRabbit\Entity\AbstractDaemon;
use STRoboTruck\Service\DataCollection\DataCollector;

class FileDownloadStepDaemon extends BaseAnalysisStepDaemon
{
    public const RABBITMQ_EXCHANGE_NAME = 'delay';

    /**
     *
     * @var int
     */
    protected int $attemptsNumber = 10;

    /**
     *
     * @var int
     */
    protected int $delay = 5 * 60 * 1000;

    /**
     *
     * Class constructor
     */
    public function __construct(DataCollector $dataCollector)
    {
        parent::__construct($dataCollector);

        $this->exchangeName = static::RABBITMQ_EXCHANGE_NAME;
    }

    /**
     *
     * @return AbstractDaemon
     */
    public function init(): AbstractDaemon
    {
        $this->getChannel()->exchange_declare(
            exchange: static::RABBITMQ_EXCHANGE_NAME,
            type: 'x-delayed-message',
            passive: false,
            durable: true,
            auto_delete: false,
            internal: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-delayed-type' => \PhpAmqpLib\Exchange\AMQPExchangeType::DIRECT,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-dead-letter-exchange' => 'delayed',
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        $this->getChannel()->queue_bind($this->getQueueName(), $this->getExchangeName(), $this->getQueueName());
        return $this;
    }
}
