<?php

declare(strict_types=1);

namespace STCall\Daemon\Analysis;

use STRabbit\Entity\AbstractDaemon;

class TranscribingDistributionStepDaemon extends BaseAnalysisStepDaemon
{
    /**
     *
     * @inheritdoc
     */
    public function init(): AbstractDaemon
    {
        $this->getChannel()->queue_declare(
            queue: $this->getQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
            nowait: false,
            arguments: new \PhpAmqpLib\Wire\AMQPTable([
                'x-max-priority' => 100,
            ]),
        );
        $this->getChannel()->queue_declare(
            queue: $this->getErrorQueueName(),
            passive: false,
            durable: true,
            exclusive: false,
            auto_delete: false,
        );
        return $this;
    }
}
