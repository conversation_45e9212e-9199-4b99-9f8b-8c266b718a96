<?php

declare(strict_types=1);

namespace STCall\ServiceProviding;

use ST<PERSON>all\Data\BillingRepository;
use STCompany\Service\Interfaces\BillingRepositoryInterface as CompanyBillingRepositoryInterface;

final readonly class CallServiceProvider implements CompanyBillingRepositoryInterface
{
    public function __construct(private BillingRepository $billingRepository)
    {
    }

    public function getCallsSecondsDurationByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        return $this->billingRepository->getCallsSecondsDurationByPeriod($companyId, $startDate, $endDate);
    }

    public function getChatsByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        return $this->billingRepository->getChatsByPeriod($companyId, $startDate, $endDate);
    }

    public function getSummarizationsByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        return $this->billingRepository->getSummarizationsByPeriod($companyId, $startDate, $endDate);
    }

    public function getChecklistCallsByPeriod(
        int $companyId,
        int $checklistId,
        string $startDate,
        string $endDate
    ): int {
        return $this->billingRepository->getChecklistCallsByPeriod($companyId, $checklistId, $startDate, $endDate);
    }
}
