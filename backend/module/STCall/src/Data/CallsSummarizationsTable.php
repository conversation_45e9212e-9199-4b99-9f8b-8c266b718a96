<?php

declare(strict_types=1);

namespace STCall\Data;

use ReflectionException;
use ST<PERSON>all\Entity\CallSummarization;
use STClickhouse\Client\Client;
use STClickhouse\Entity\BaseTable;
use STLib\Mvc\Hydrator\Hydrator;

class CallsSummarizationsTable extends BaseTable
{
    public function __construct(Client $client, private readonly Hydrator $hydrator)
    {
        parent::__construct($client);
    }

    public function saveCallSummarization(CallSummarization $callSummarization): void
    {
        $data = $callSummarization->toArray();

        $this->getClient()->insert($this->getTableName(), [$data], array_keys($data));
    }

    public function removeCallSummarization(string $callId, int $companyId): void
    {
        $this->getClient()->softDelete($this->getTableDataName(), [
            [
                'call_id',
                '=',
                $callId
            ],
            [
                'company_id',
                '=',
                $companyId
            ]
        ]);
    }

    /**
     * @throws ReflectionException
     */
    public function getCallSummarization(string $callId, int $companyId): ?CallSummarization
    {
        $params = [
            'table' => $this->getTableName(),
            'call_id' => $callId,
            'company_id' => $companyId,
        ];

        $query = $this->getClient()
            ->getConnection()
            ->select(
                'SELECT * FROM {table} WHERE call_id = :call_id AND company_id = :company_id',
                $params
            );

        return !empty($query->rows()) ? $this->hydrator->hydrateClass(
            current($query->rows()),
            CallSummarization::class,
            true
        ) : null;
    }
}
