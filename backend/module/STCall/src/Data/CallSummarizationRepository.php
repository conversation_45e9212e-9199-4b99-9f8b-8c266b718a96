<?php

declare(strict_types=1);

namespace STCall\Data;

use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;

class CallSummarizationRepository extends BaseTable
{
    use QueriesTrait;

    public function getExtendedSummarizationData(string $callId, int $companyId): array
    {
        $bindings = [
            'call_id' => $callId,
            'company_id' => $companyId,
            'calls_summarizations_table' => 'calls_summarizations',
        ];

        $summarizationQuery = '
            SELECT 
                call_id, 
                company_id,
                key_points,
                overview, 
                details,
                customer_sentiment, 
                next_steps, 
                primary_purpose, 
                main_topics, 
                customer_problems, 
                key_action_items, 
                business_opportunities, 
                risks, 
                conversation_type
            FROM {calls_summarizations_table}
            WHERE call_id = :call_id AND company_id = :company_id
        ';

        $callsQuery = $this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'call_id',
                'company_id',
            ],
            'created_at',
            ['call_time', 'agent_id', 'client_id'],
            where: [
                'company_id' => $companyId,
                'call_id' => $callId,
            ]
        );

        $sql = '
            SELECT
                c.call_id as call_id,
                c.call_time,
                c.company_id as company_id,
                s.details,
                s.overview,
                s.key_points,
                s.customer_sentiment,
                s.next_steps,
                s.primary_purpose,
                s.main_topics,
                s.customer_problems,
                s.key_action_items,
                s.business_opportunities,
                s.risks,
                s.conversation_type,
                c.agent_id,
                u.user_name as agent_name,
                c.client_id
            FROM (' . $summarizationQuery . ') s
            LEFT JOIN (' . $callsQuery . ') c ON s.call_id = c.call_id AND s.company_id = c.company_id
            LEFT JOIN
                (
                    SELECT
                        *
                    FROM
                        dictionary(users)
                ) u
                ON u.user_id = c.agent_id
        ';

        $result = $this->getClient()->selectAll($sql, $bindings);
        if (empty($result)) {
            return [];
        }

        $data = current($result);

        if (!$data['client_id']) {
            $data['client_name'] = '';

            return $data;
        }

        $clientSql = $this->getFinalTableSqlUsingGroupBy(
            'clients',
            [
                'company_id',
                'client_id',
            ],
            'created',
            [
                'client_name',
            ],
            [
                'company_id' => $companyId,
                'client_id' => $data['client_id'],
            ]
        );

        $clientResult = $this->getClient()->selectOne($clientSql);

        if (!empty($clientResult)) {
            $data['client_name'] = $clientResult['client_name'];
        } else {
            $data['client_name'] = '';
        }

        return $data;
    }
}
