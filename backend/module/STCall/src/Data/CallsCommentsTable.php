<?php

declare(strict_types=1);

namespace STCall\Data;

class CallsCommentsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param string $commentId
     * @return array
     */
    public function getComment(int $companyId, int $userId, string $commentId): array
    {
        $sql = '
            SELECT
                cc.comment_id comment_id,
                cc.company_id company_id,
                cc.call_id call_id,
                cc.user_id user_id,
                cc.message_body message_body,
                u.user_name user_name,
                u.user_email user_email,
                cc.created created
            FROM
            (
                ' . $this->getFinalTableSql('calls_comments', ['comment_id'], 'created') . '
            ) cc
            INNER JOIN
                dictionary(users) u
                ON u.user_id = cc.user_id
            WHERE
                cc.company_id = \'' . $companyId . '\'
                AND cc.user_id = \'' . $userId . '\'
                AND cc.comment_id = \'' . $commentId . '\'
        ';
        $result = $this->getClient()->selectOne($sql);
        if (is_null($result)) {
            throw new \STApi\Entity\Exception\NotFoundApiException('Comment not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param string|array $callIds
     * @param int $userId
     * @return array
     */
    public function getComments(int $companyId, string|array $callIds, int $userId = null): array
    {
        $arrayCallIds = is_string($callIds) ? [$callIds] : $callIds;
        if (count($arrayCallIds) === 0) {
            return [];
        }
        $sql = '
            SELECT
        ';
        if (is_int($userId)) {
            $sql .= '
                n.is_unread is_unread_by_active_user,
            ';
        }
        $sql .= '
                cc.comment_id comment_id,
                cc.company_id company_id,
                cc.call_id call_id,
                cc.user_id user_id,
                cc.message_body message_body,
                u.user_name user_name,
                u.user_email user_email,
                cc.created created
        ';
        $sql .= '
            FROM
            (
                ' . $this->getFinalTableSql('calls_comments', ['comment_id'], 'created') . '
            ) cc
        ';
        if (is_int($userId)) {
            $sql .= '
                LEFT JOIN
                (
                    ' . $this->getFinalTableSql('calls_comments_notifications', [
                        'comment_id',
                        'user_id'
                    ], 'created') . '
                ) n
                ON n.comment_id = cc.comment_id
                AND n.user_id = \'' . $userId . '\'
            ';
        }
        $sql .= '
            INNER JOIN
                dictionary(users) u
                ON u.user_id = cc.user_id
            WHERE
                cc.company_id = \'' . $companyId . '\'
                AND cc.call_id IN (\'' . implode('\',\'', $arrayCallIds) . '\')
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param \STCall\Entity\Comment $comment
     * @return int
     */
    public function saveComment(\STCall\Entity\Comment $comment): int
    {
        $commentCollection = new \STCall\Entity\CommentCollection();
        $commentCollection->add($comment);
        return $this->saveComments($commentCollection);
    }

    /**
     *
     * @param \STCall\Entity\CommentCollection $commentCollection
     * @return int
     */
    public function saveComments(\STCall\Entity\CommentCollection $commentCollection): int
    {
        if ($commentCollection->count() === 0) {
            return 0;
        }
        foreach ($commentCollection->chunk(100) as $commentsChunk) {
            $data = [];
            foreach ($commentsChunk as $comment) {
                $record = $comment->toArray();
                unset($record['is_unread_by_active_user']);
                unset($record['message_body_with_mentions']);
                unset($record['mentioned_user_ids']);
                unset($record['user']);
                $data[] = $record;
            }
            $columns = array_keys(current($data));
            $this->getClient()->insert($this->getTableName(), $data, $columns);
        }
        return $commentCollection->count();
    }
}
