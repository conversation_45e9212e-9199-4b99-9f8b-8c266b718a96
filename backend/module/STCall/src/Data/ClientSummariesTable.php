<?php

declare(strict_types=1);

namespace STCall\Data;

use Carbon\Carbon;
use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;
use ST<PERSON>all\Entity\ClientSummary;

class ClientSummariesTable extends BaseTable
{
    use QueriesTrait;

    public function create(ClientSummary $clientSummary): void
    {
        $data = $clientSummary->toArray();

        if (isset($data['last_call_time']) && $data['last_call_time'] instanceof Carbon) {
            $data['last_call_time'] = $data['last_call_time']->toDateTimeString();
        }

        if (isset($data['last_call_created']) && $data['last_call_created'] instanceof Carbon) {
            $data['last_call_created'] = $data['last_call_created']->toDateTimeString();
        }

        if (isset($data['created']) && $data['created'] instanceof Carbon) {
            $data['created'] = $data['created']->toDateTimeString();
        } else {
            $data['created'] = Carbon::now()->toDateTimeString();
        }

        if (isset($data['timeline'])) {
            $data['timeline'] = array_reduce($data['timeline'], function ($result, array $timelineItem) {
                $result[] = $this->convertToMap([
                    'call_id' => $timelineItem['call_id'],
                    'call_time' => $timelineItem['call_time'],
                    'description' => $timelineItem['description'],
                ]);
                return $result;
            }, []);
        }

        if (isset($data['customer_problems'])) {
            $data['customer_problems'] = array_reduce($data['customer_problems'], function ($result, array $customerProblem) {
                $result[] = $this->convertToMap([
                    'status' => $customerProblem['status'],
                    'importance' => $customerProblem['importance'],
                    'description' => $customerProblem['description'],
                ]);
                return $result;
            }, []);
        }

        $this->getClient()->insert($this->getTableName(), [$data], array_keys($data));
    }

    public function getLastSummary(int $companyId, string $clientId, ?Carbon $withLastCallBeforeDate = null): array|null
    {
        $bindings = [
            'companyId' => $companyId,
            'clientId' => $clientId,
        ];
        $where = ' WHERE company_id = :companyId AND client_id = :clientId';

        if ($withLastCallBeforeDate !== null) {
            $bindings['date'] = $withLastCallBeforeDate->toDateTimeString();
            $where .= ' AND last_call_time < :date';
        }

        $sql = <<<SQL
            SELECT *
            FROM client_summaries
            {$where}
            ORDER BY created DESC
            LIMIT 1
        SQL;

        $result = $this->getClient()->selectAll($sql, $bindings);

        if (empty($result)) {
            return null;
        }

        return $result[0];
    }

    public function removeClientSummary(int $companyId, ?string $clientId = null): void
    {
        $where = [
            [
                'company_id',
                '=',
                $companyId,
            ],
        ];

        if ($clientId !== null) {
            $where[] = [
                'client_id',
                '=',
                $clientId,
            ];
        }

        $this->getClient()->softDelete($this->getTableDataName(), $where);
    }
}
