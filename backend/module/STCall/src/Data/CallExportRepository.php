<?php

declare(strict_types=1);

namespace STCall\Data;

use Carbon\Carbon;
use ST<PERSON>lickhouse\Client\Client;
use ST<PERSON><PERSON>house\Data\QueriesTrait;
use ST<PERSON>lickhouse\Entity\BaseTable;
use STCompany\Entity\Company;
use STFront\Service\FrontService;

class CallExportRepository extends BaseTable
{
    use QueriesTrait;

    public function __construct(
        private readonly FrontService $frontService,
        Client $client
    ) {
        parent::__construct($client);
    }

    public function getCallsCsvExport(int $companyId, Carbon $startDate, Carbon $endDate): ?string
    {
        $callIds = $this->getCallIds($companyId, $startDate, $endDate);
        if (empty($callIds)) {
            return null;
        }

        $summarizationQuery = '
            SELECT 
                call_id, 
                company_id,
                key_points,
                overview, 
                details,
                customer_sentiment, 
                next_steps, 
                primary_purpose, 
                main_topics, 
                customer_problems, 
                key_action_items, 
                business_opportunities, 
                risks, 
                conversation_type
            FROM {calls_summarizations_table}
            WHERE company_id = :company_id AND call_id IN (:call_ids)
        ';

        $clientsQuery = $this->getClientsQuery($companyId);

        $bindings = [
            'company_id' => $companyId,
            'calls_summarizations_table' => 'calls_summarizations',
        ];

        $withNames = true;
        $result = '';
        foreach (array_chunk($callIds, 4000) as $chunk) {
            $callsQuery = $this->getCallsQuery($companyId, $chunk);
            $bindings['call_ids'] = $chunk;

            $domain = $this->frontService->getActiveFront()->getDomain();
            $callLinkPart1 = 'https://' . $domain . '/' . $companyId . '/company-name/0/search?search=';
            $callLinkPart2 = '&endpoint=calls';
            $sql = '
            SELECT c.call_id as call_id,
                   c.company_id as company_id,
                   c.call_time,
                   c.call_type,
                   c.call_language,
                   c.call_status,
                   c.call_duration,
                   c.agent_id,
                   u.user_name as agent_name,
                   c.client_id as client_id,
                   concat(\'' . $callLinkPart1 . '\', c.call_id, \'' . $callLinkPart2 . '\') as link,
                   cl.client_name,
                   cl.country as client_country,
                   cl.status as client_status,
                   cl.source as client_source,
                   cl.acquisition_date as client_acquisition_date,
                   cl.is_converted as client_is_converted,
                   cl.converted_date as client_converted_date,
                   cl.last_transaction_date as client_last_transaction_date,
                   cl.campaign_id as client_campaign_id,
                   cl.value as client_value,
                   s.details,
                   s.overview,
                   s.key_points,
                   s.customer_sentiment,
                   s.next_steps,
                   s.primary_purpose,
                   s.main_topics,
                   s.customer_problems,
                   s.key_action_items,
                   s.business_opportunities,
                   s.risks,
                   s.conversation_type
            FROM (' . $callsQuery . ') c
            LEFT JOIN (' . $summarizationQuery . ') s ON s.call_id = c.call_id AND s.company_id = c.company_id
            LEFT JOIN ( SELECT * FROM dictionary(users) ) u ON u.user_id = c.agent_id
            LEFT JOIN (' . $clientsQuery . ') cl ON cl.client_id = c.client_id AND cl.company_id = c.company_id
        ';

            $result .= $this->getClient()->selectCsv($sql, $bindings, $withNames);

            $withNames = false;
        }

        return $result;
    }

    private function getCallsQuery(int $companyId, array $callIds): string
    {
        return $this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'call_id',
                'company_id',
            ],
            'created_at',
            additionalColumns: [
                'call_time',
                'call_type',
                'call_language',
                'call_status',
                'call_duration',
                'agent_id',
                'client_id',
            ],
            where: [
                'company_id' => $companyId,
                'call_id' => $callIds,
            ]
        );
    }

    private function getClientsQuery(int $companyId): string
    {
        $clientIdsQuery = '(SELECT DISTINCT client_id FROM calls WHERE call_id IN (:call_ids))';

        return $this->getFinalTableSqlUsingGroupBy(
            'clients',
            [
                'client_id',
                'company_id',
            ],
            'created',
            additionalColumns: [
                'client_name',
                'country',
                'status',
                'source',
                'acquisition_date',
                'is_converted',
                'converted_date',
                'last_transaction_date',
                'campaign_id',
                'value',
            ],
            where: [
                'company_id' => $companyId,
                [
                    'type' => 'expression',
                    'value' => 'client_id IN ' . $clientIdsQuery,
                ]
            ]
        );
    }

    public function getParagraphsCsvExport(Company $company, Carbon $startDate, Carbon $endDate): ?string
    {
        $callIds = $this->getCallIds($company->getId(), $startDate, $endDate);
        if (empty($callIds)) {
            return null;
        }

        $withNames = true;
        $result = '';
        foreach (array_chunk($callIds, 4000) as $chunk) {
            $paragraphsQuery = $this->getParagraphsQuery($company->getId(), $chunk);

            $sql = '
                SELECT
                    p.company_id as company_id,
                    p.call_id as call_id,
                    p.paragraph_number as paragraph_number,
                    p.speaker_role,
                    p.start_time,
                    p.end_time,
                    p.timestamp,
                    decrypt(\'aes-256-ofb\', p.text, \'' . $company->getEncryptionKey() . '\') text,
                    decrypt(\'aes-256-ofb\', p.en_text, \'' . $company->getEncryptionKey() . '\') en_text
                FROM (' . $paragraphsQuery . ') p
                ORDER BY call_id, paragraph_number
            ';

            $result .= $this->getClient()->selectCsv($sql, withNames: $withNames);

            $withNames = false;
        }

        if (substr_count($result, "\n") === 1) {
            return null;
        }

        return $result;
    }

    private function getParagraphsQuery(int $companyId, array $callIds): string
    {
        return $this->getFinalTableSqlUsingGroupBy(
            'calls_paragraphs',
            [
                'company_id',
                'call_id',
                'paragraph_number',
            ],
            'created',
            additionalColumns: [
                'speaker_role',
                'start_time',
                'end_time',
                'timestamp',
                'text',
                'en_text',
            ],
            where: [
                'company_id' => $companyId,
                'call_id' => $callIds,
            ]
        );
    }

    public function getEventsCsvExport(int $companyId, Carbon $startDate, Carbon $endDate): ?string
    {
        $callIds = $this->getCallIds($companyId, $startDate, $endDate);
        if (empty($callIds)) {
            return null;
        }

        $withNames = true;
        $result = '';
        foreach (array_chunk($callIds, 4000) as $chunk) {
            $eventsQuery = $this->getEventsQuery($companyId, $chunk);

            $sql = '
                SELECT
                    e.company_id as company_id,
                    e.call_id as call_id,
                    e.role_id,
                    e.paragraph as paragraph_number,
                    e.paragraph_speaker_role,
                    e.event_id,
                    e.event_name,
                    e.event_score,
                    e.event_highlight,
                    e.event_en_highlight,
                    e.event_text,
                    e.event_en_text,
                    e.event_category_name
                FROM (' . $eventsQuery . ') e
                WHERE e.event_is_deleted = 0
                ORDER BY company_id, call_id
            ';

            $result .= $this->getClient()->selectCsv($sql, withNames: $withNames);

            $withNames = false;
        }

        if (substr_count($result, "\n") === 1) {
            return null;
        }

        return $result;
    }

    private function getEventsQuery(int $companyId, array $callIds): string
    {
        return $this->getFinalTableSqlUsingGroupBy(
            'precalculated_calls_events',
            [
                'company_id',
                'role_id',
                'call_id',
                'paragraph',
                'event_id',
            ],
            'created',
            additionalColumns: [
                'paragraph_speaker_role',
                'event_name',
                'event_score',
                'event_highlight',
                'event_en_highlight',
                'event_text',
                'event_en_text',
                'event_category_name',
                'event_is_deleted'
            ],
            where: [
                'company_id' => $companyId,
                'call_id' => $callIds,
            ]
        );
    }

    private function getCallIds(int $companyId, Carbon $startDate, Carbon $endDate): array
    {
        $groupSql = $this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'call_id',
                'company_id',
            ],
            'created_at',
            ['is_deleted'],
            [
                'company_id' => $companyId,
                'start_date' => [
                    'column' => 'analyzed_at',
                    'value' => $startDate,
                    'type' => 'date',
                    'compare' => '>=',
                ],
                'end_date' => [
                    'column' => 'analyzed_at',
                    'value' => $endDate,
                    'type' => 'date',
                    'compare' => '<=',
                ]
            ],
        );

        $sql = 'SELECT call_id FROM (' . $groupSql . ') WHERE is_deleted = 0';

        $result = $this->getClient()->selectAll($sql);

        return array_values(array_column($result, 'call_id'));
    }
}
