<?php

declare(strict_types=1);

namespace STCall\Data;

use ST<PERSON>all\Entity\Call;
use STCall\Entity\ChatCall;
use STClickhouse\Data\QueriesTrait;
use STClickhouse\Entity\BaseTable;

final class BillingRepository extends BaseTable
{
    use QueriesTrait;

    public function getCallsSecondsDurationByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        $groupSql = $this->getCallsGroupSql($companyId, $startDate, $endDate, Call::CALL_TYPE);

        $sql = 'SELECT SUM(call_duration) duration_in_seconds FROM (' . $groupSql . ')';

        $result = $this->getClient()->selectAll($sql);

        if (empty($result)) {
            return 0;
        }

        return (int) current($result)['duration_in_seconds'];
    }

    public function getChatsByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        $groupSql = $this->getCallsGroupSql($companyId, $startDate, $endDate, ChatCall::CHAT_CALL_TYPE);

        $sql = 'SELECT COUNT(*) count FROM (' . $groupSql . ')';

        $result = $this->getClient()->selectAll($sql);

        if (empty($result)) {
            return 0;
        }

        return (int) current($result)['count'];
    }

    public function getSummarizationsByPeriod(int $companyId, string $startDate, string $endDate): int
    {
        $callIds = $this->getCallIds($companyId, $startDate, $endDate);
        if (empty($callIds)) {
            return 0;
        }

        $params = [
            'table' => 'calls_summarizations',
            'call_ids' => $callIds,
            'company_id' => $companyId,
        ];

        $query = $this->getClient()
            ->getConnection()
            ->select(
                'SELECT COUNT(*) as count FROM {table} WHERE call_id IN (:call_ids) AND company_id = :company_id',
                $params
            );

        return !empty($query->rows()) ? (int) current($query->rows())['count'] : 0;
    }

    public function getChecklistCallsByPeriod(
        int $companyId,
        int $checklistId,
        string $startDate,
        string $endDate
    ): int {
        $callIds = $this->getCallIds($companyId, $startDate, $endDate);
        if (empty($callIds)) {
            return 0;
        }

        $params = [
            'table' => 'calls_checklists_points',
            'call_ids' => $callIds,
            'company_id' => $companyId,
            'checklist_id' => $checklistId,
        ];

        $query = $this->getClient()
            ->getConnection()
            ->select(
                '
                SELECT COUNT(DISTINCT ccp.call_id) as count
                FROM {table} ccp
                INNER JOIN dictionary(checklists_points) cp ON cp.checklist_point_id = ccp.checklist_point_id
                WHERE
                    ccp.call_id IN (:call_ids) AND
                    cp.company_id = :company_id AND
                    cp.checklist_id = :checklist_id',
                $params
            );

        return !empty($query->rows()) ? (int) current($query->rows())['count'] : 0;
    }

    private function getCallIds(int $companyId, string $startDate, string $endDate): array
    {
        $groupSql = $this->getCallsGroupSql($companyId, $startDate, $endDate);

        $sql = 'SELECT call_id FROM (' . $groupSql . ')';

        $result = $this->getClient()->selectAll($sql);

        return array_values(array_column($result, 'call_id'));
    }

    private function getCallsGroupSql(int $companyId, string $startDate, string $endDate, string $type = null): string
    {
        $filters = [
            'company_id' => $companyId,
            'start_date' => [
                'column' => 'uploaded_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ],
            'end_date' => [
                'column' => 'uploaded_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ]
        ];

        if (!is_null($type)) {
            $filters['call_type'] = $type;
        }

        return $this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'call_id',
                'company_id',
            ],
            'created_at',
            [
                'call_duration',
            ],
            $filters
        );
    }
}
