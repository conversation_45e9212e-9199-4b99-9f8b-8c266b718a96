<?php

declare(strict_types=1);

namespace STCall\Data;

use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;

class CallsParagraphsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param int $companyId
     * @param string $callId
     * @param int $paragraphNumber
     * @param string $key
     * @return array
     * @throws NotFoundApiException
     */
    public function getParagraph(int $companyId, string $callId, int $paragraphNumber, string $key): array
    {
        $sql = '
            SELECT
                company_id,
                call_id,
                call_time,
                paragraph_number,
                speaker_number,
                speaker_role,
                start_time,
                end_time,
                timestamp,
                decrypt(\'aes-256-ofb\', text, \'' . $key . '\') text,
                decrypt(\'aes-256-ofb\', en_text, \'' . $key . '\') en_text,
                created
            FROM
            (
                ' .
                $this->getFinalTableSqlUsingGroupBy(
                    'calls_paragraphs',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                    ],
                    'created',
                    [
                        'call_time',
                        'speaker_number',
                        'speaker_role',
                        'start_time',
                        'end_time',
                        'timestamp',
                        'text',
                        'en_text',
                        'created',
                    ],
                    [
                        'company_id' => $companyId,
                        'call_id' => $callId,
                        'paragraph_number' => $paragraphNumber,
                    ]
                ) . '
            )
            WHERE
                company_id = \'' . $companyId . '\'
                AND call_id = \'' . $callId . '\'
                AND paragraph_number = \'' . $paragraphNumber . '\'
        ';
        $result = $this->getClient()->selectOne($sql);
        if (is_null($result)) {
            throw new NotFoundApiException('Paragraph not found');
        }
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param string|array $callIds
     * @param string $key
     * @return array
     */
    public function getParagraphs(int $companyId, string|array $callIds, string $key): array
    {
        $arrayCallIds = is_string($callIds) ? [$callIds] : $callIds;
        if (count($arrayCallIds) === 0) {
            return [];
        }
        $sql = '
            SELECT
                company_id,
                call_id,
                call_time,
                paragraph_number,
                speaker_number,
                speaker_role,
                start_time,
                end_time,
                timestamp,
                decrypt(\'aes-256-ofb\', text, \'' . $key . '\') text,
                decrypt(\'aes-256-ofb\', en_text, \'' . $key . '\') en_text,
                created
            FROM
            (
                ' .
                // phpcs:disable
                $this->getFinalTableSqlUsingGroupBy(
                    'calls_paragraphs',
                    [
                        'company_id',
                        'call_id',
                        'paragraph_number',
                    ],
                    'created',
                    [
                        'call_time',
                        'speaker_number',
                        'speaker_role',
                        'start_time',
                        'end_time',
                        'timestamp',
                        'text',
                        'en_text',
                        'created',
                    ],
                    [
                        'company_id' => $companyId,
                        'call_id' => $callIds,
                    ]
                )
                // phpcs:enable
                . '
            )
            ORDER BY
                paragraph_number ASC
        ';
        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param \STCall\Entity\Paragraph $paragraph
     * @param string $key
     * @return int
     */
    public function saveParagraph(\STCall\Entity\Paragraph $paragraph, string $key): int
    {
        $paragraphCollection = new ParagraphCollection();
        $paragraphCollection->add($paragraph);
        return $this->saveParagraphs($paragraphCollection, $key);
    }

    /**
     *
     * @param ParagraphCollection $paragraphCollection
     * @param string $key
     * @return int
     */
    public function saveParagraphs(ParagraphCollection $paragraphCollection, string $key): int
    {
        if ($paragraphCollection->count() === 0) {
            return 0;
        }
        foreach ($paragraphCollection->chunk(100) as $paragraphsChunk) {
            $data = [];
            foreach ($paragraphsChunk as $paragraph) {
                /** @var Paragraph $paragraph */
                $record = $paragraph->toArray([
                    'company_id',
                    'call_id',
                    'call_time',
                    'paragraph_number',
                    'speaker_number',
                    'speaker_role',
                    'start_time',
                    'end_time',
                    'timestamp',
                    'text',
                    'en_text',
                    'created',
                ]);
                $escapedText = $this->getClient()->escapeChars($record['text']);
                $record['text'] = new \ClickHouseDB\Query\Expression\Raw("encrypt('aes-256-ofb', '" . $escapedText . "', '" . $key . "')");
                if (!is_null($record['en_text'])) {
                    $escapedEnText = $this->getClient()->escapeChars($record['en_text']);
                    $record['en_text'] = new \ClickHouseDB\Query\Expression\Raw("encrypt('aes-256-ofb', '" . $escapedEnText . "', '" . $key . "')");
                }
                $record['created'] = \Carbon\Carbon::now();
                $data[] = $record;
            }
            $columns = array_keys(current($data));
            $this->getClient()->insert($this->getTableName(), $data, $columns);
        }
        return $paragraphCollection->count();
    }

    /**
     *
     * @param \STCall\Entity\Call $call
     * @return void
     */
    public function deleteByCall(\STCall\Entity\Call $call): void
    {
        $this->getClient()->softDelete($this->getTableDataName(), [['call_id', '=', $call->getId()]]);
    }
}
