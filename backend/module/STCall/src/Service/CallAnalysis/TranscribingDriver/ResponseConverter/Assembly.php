<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use STCall\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Assembly extends AbstractParagraphResponseConverter
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = new \STCall\Entity\ParagraphCollection();

        foreach ($response->utterances as $paragraphNumber => $paragraphData) {
            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphNumber,
                'start_time' => (float) $paragraphData->start / 1000,
                'end_time' => (float) $paragraphData->end / 1000,
                'speaker_number' => $this->letterToNumber($paragraphData->speaker),
                'text' => $paragraphData->text,
            ], \STCall\Entity\Paragraph::class));
        }

        return $paragraphCollection;
    }
}
