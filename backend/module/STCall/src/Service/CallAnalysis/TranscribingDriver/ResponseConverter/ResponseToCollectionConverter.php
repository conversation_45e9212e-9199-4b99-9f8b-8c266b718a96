<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use Exception;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ResponseToCollectionConverter
{
    use BaseHydratorTrait;

    public const WORDCAB = 1;
    public const DEEPGRAM = 2;
    public const AWS = 3;
    public const DEEPGRAM_WHISPER = 4;
    public const SPEECHMATICS = 5;
    public const ASSEMBLY = 6;
    public const TRANSCRIPTOR = 7;
    public const SALAD = 8;

    /**
     *
     * @param mixed $response
     * @param int $type
     * @return ParagraphCollection
     * @throws Exception
     */
    public function convertDriverResponseToParagraphCollection(mixed $response, int $type, Call $call): ParagraphCollection
    {
        $paragraphCollection = (new ResponseConverterFactory())->create($type)->convert($response);

        foreach ($paragraphCollection as $paragraph) {
            $paragraph->setCompanyId($call->getCompanyId())
                ->setCallId($call->getId())
                ->setCallTime($call->getTime())
            ;
        }

        return $paragraphCollection;
    }
}
