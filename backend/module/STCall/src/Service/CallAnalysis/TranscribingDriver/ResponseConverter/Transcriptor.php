<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use STCall\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Transcriptor extends AbstractParagraphResponseConverter
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = new \STCall\Entity\ParagraphCollection();

        foreach ($response['content'] as $paragraphNumber => $paragraphData) {
            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphNumber,
                'start_time' => (float) $paragraphData['StartTime'] / 1000,
                'end_time' => (float) $paragraphData['EndTime'] / 1000,
                'speaker_number' => preg_replace('/\D/', '', $paragraphData['Speaker']),
                'text' => $paragraphData['text'],
            ], \STCall\Entity\Paragraph::class));
        }

        return $paragraphCollection;
    }
}
