<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\Factory;

use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STRoboTruck\Service\DataCollection\DataCollector;

interface FactoryInterface
{
    /**
     *
     * @param DataCollector $dataCollector
     * @return AbstractDriver
     */
    public function create(DataCollector $dataCollector): AbstractDriver;
}
