<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\Checklist;

use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCall\Service\Interfaces\TeamSelectorInterface;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Company;
use STCompany\Entity\Team;

class ChecklistRunChecker
{
    public function __construct(
        private readonly FeaturesAvailableCheckerInterface $featuresAvailableChecker,
        private readonly TeamSelectorInterface $teamSelector,
        private readonly CallsTable $callsTable,
    ) {
    }

    public function shouldRunChecklist(Call $call, Company $company, Checklist $checklist): bool
    {
        if (!$this->featuresAvailableChecker->isChecklistAvailable($checklist->getId(), $company)) {
            return false;
        }

        if ($checklist->getCallDurationThreshold() > $call->getDuration()) {
            return false;
        }

        if (!empty($checklist->getCallsStatuses())) {
            if (!in_array($call->getCallStatus(), $checklist->getCallsStatuses(), true)) {
                return false;
            }
        }

        if (!empty($checklist->getCallsTeams())) {
            /** @var Team $agentTeam */
            $agentTeam = $this->teamSelector->getTeams($company->getId(), $call->getAgentId())->first();

            if (!$agentTeam instanceof Team) {
                return false;
            }

            if (!in_array($agentTeam->getId(), $checklist->getCallsTeams(), true)) {
                return false;
            }
        }

        if ($checklist->getCallsScope() !== Checklist::CALLS_SCOPE_ALL) {
            $clientCallsCount = $this->callsTable->getCallsCountByClientId($company->getId(), $call->getClientId());

            if ($clientCallsCount > 1) {
                return false;
            }
        }

        return true;
    }
}
