<?php

declare(strict_types=1);

namespace STCall\Service\Import\UploadParams;

class UploadParams
{
    /**
     *
     * @var string
     */
    protected string $driverName;

    /**
     *
     * @var string|null
     */
    protected ?string $content = null;

    /**
     *
     * @var null|string|array
     */
    protected null|string|array $sourcedContent;

    /**
     *
     * @var \STCompany\Entity\Company
     */
    protected \STCompany\Entity\Company $company;

    /**
     *
     * @var \STUser\Entity\User|null
     */
    protected ?\STUser\Entity\User $user = null;

    /**
     *
     * @var array
     */
    protected array $options = [];

    /**
     *
     * @return string
     */
    public function getDriverName(): string
    {
        return $this->driverName;
    }

    /**
     *
     * @return string|null
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     *
     * @return null|string|array
     */
    public function getSourcedContent(): null|string|array
    {
        return $this->sourcedContent;
    }

    /**
     *
     * @return \STCompany\Entity\Company
     */
    public function getCompany(): \STCompany\Entity\Company
    {
        return $this->company;
    }

    /**
     *
     * @return \STUser\Entity\User|null
     */
    public function getUser(): ?\STUser\Entity\User
    {
        return $this->user;
    }

    /**
     *
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     *
     * @param string $driverName
     * @return UploadParams
     */
    public function setDriverName(string $driverName): UploadParams
    {
        $this->driverName = $driverName;
        return $this;
    }

    /**
     *
     * @param string|null $content
     * @return UploadParams
     */
    public function setContent(?string $content): UploadParams
    {
        $this->content = $content;
        return $this;
    }

    /**
     *
     * @param null|string|array $sourcedContent
     * @return UploadParams
     */
    public function setSourcedContent(null|string|array $sourcedContent): UploadParams
    {
        $this->sourcedContent = $sourcedContent;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return UploadParams
     */
    public function setCompany(\STCompany\Entity\Company $company): UploadParams
    {
        $this->company = $company;
        return $this;
    }

    /**
     *
     * @param \STUser\Entity\User|null $user
     * @return UploadParams
     */
    public function setUser(?\STUser\Entity\User $user): UploadParams
    {
        $this->user = $user;
        return $this;
    }

    /**
     *
     * @param array $options
     * @return UploadParams
     */
    public function setOptions(array $options): UploadParams
    {
        $this->options = $options;
        return $this;
    }
}
