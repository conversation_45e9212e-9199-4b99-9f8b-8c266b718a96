<?php

declare(strict_types=1);

namespace STCall\Service\Interfaces;

use STCall\Entity\Call;
use STCompany\Entity\Company;

interface FeaturesAvailableCheckerInterface
{
    public function isCallAnalysisAvailable(Company $company): bool;

    public function isChatAnalysisAvailable(Company $company): bool;

    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool;

    public function isSummarizationAvailable(Company $company): bool;

    public function isChecklistAvailable(int $checklistId, Company $company): bool;
}
