<?php

declare(strict_types=1);

namespace STCall\Service\Export;

use Carbon\Carbon;
use RuntimeException;
use STCall\Data\CallExportRepository;
use STCompany\Entity\Company;

class CallExportGenerator
{
    public const string EXPORT_TYPE_CALLS = 'calls';
    public const string EXPORT_TYPE_PARAGRAPHS = 'paragraphs';
    public const string EXPORT_TYPE_EVENTS = 'events';

    public function __construct(
        private readonly CallExportRepository $callExportRepository,
        private readonly FileNameGenerator $fileNameGenerator,
        private readonly ExternalFileService $externalFileService
    ) {
    }

    public function generate(Company $company, Carbon $startDate, Carbon $endDate, string $exportType): void
    {
        $exportData = match ($exportType) {
            self::EXPORT_TYPE_CALLS => $this->callExportRepository->getCallsCsvExport(
                $company->getId(),
                $startDate,
                $endDate
            ),
            self::EXPORT_TYPE_PARAGRAPHS => $this->callExportRepository->getParagraphsCsvExport(
                $company,
                $startDate,
                $endDate
            ),
            self::EXPORT_TYPE_EVENTS => $this->callExportRepository->getEventsCsvExport(
                $company->getId(),
                $startDate,
                $endDate
            ),
            default => throw new RuntimeException('Unknown export type: ' . $exportType),
        };

        if (is_null($exportData)) {
            return;
        }

        $fileName = $this->fileNameGenerator->generate($company, $exportType, $startDate, $endDate);

        $this->externalFileService->saveFileContent(
            $fileName,
            $exportData,
            $company->getAwsS3ExportBucketName(),
            $company->getAwsS3ExportBucketRegion()
        );
    }
}
