<?php

declare(strict_types=1);

namespace STCall\Entity;

class Comment
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    protected const COMMENT_ID_PREFIX = '';
    public const MENTION_START_DELIMITER = '{{{';
    public const MENTION_END_DELIMITER = '}}}';

    /**
     *
     * @var string|null
     */
    protected ?string $commentId = null;

    /**
     *
     * @var int
     */
    protected int $companyId;

    /**
     *
     * @var int
     */
    protected int $userId;

    /**
     *
     * @var \STCompany\Entity\User
     */
    protected \STCompany\Entity\User $user;

    /**
     *
     * @var string
     */
    protected string $callId;

    /**
     *
     * @var string
     */
    protected string $messageBody;

    /**
     *
     * @var bool|null
     */
    protected ?bool $isUnreadByActiveUser = null;

    /**
     *
     * @var \STCompany\Entity\UserCollection|null
     */
    protected ?\STCompany\Entity\UserCollection $mentionedUsers = null;

    /**
     *
     * User ids to notify (red indicator). Mentioned and who commented before. Behavior like slack treads.
     *
     * @var array
     */
    protected array $notifiedUserIds = [];

    /**
     *
     * @var \Carbon\Carbon
     */
    protected ?\Carbon\Carbon $created = null;

    /**
     *
     * Class constructor
     */
    public function __construct()
    {
        $this->user = new \STCompany\Entity\User();
        $this->mentionedUsers = new \STCompany\Entity\UserCollection();
    }

    /**
     *
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     *
     * @return string|null
     */
    public function getCommentId(): ?string
    {
        return $this->commentId;
    }

    /**
     *
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     *
     * @return \STCompany\Entity\User
     */
    public function getUser(): \STCompany\Entity\User
    {
        return $this->user;
    }

    /**
     *
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     *
     * @return string
     */
    public function getMessageBody(): string
    {
        return $this->messageBody;
    }

    /**
     *
     * @return string
     */
    public function getMessageBodyWithMentions(): string
    {
        if (is_null($this->mentionedUsers)) {
            return $this->getMessageBody();
        }
        $messageBody = $this->getMessageBody();
        foreach ($this->getMentionedUserIds() as $mentionedUserId) {
            $regex = preg_quote('/' . static::MENTION_START_DELIMITER . $mentionedUserId . static::MENTION_END_DELIMITER . '/');
            $messageBody = preg_replace($regex, $this->getMentionedUsers()->offsetGet($mentionedUserId)->getName(), $this->getMessageBody());
        }
        return $messageBody;
    }

    /**
     *
     * @return bool|null
     */
    public function getIsUnreadByActiveUser(): ?bool
    {
        return $this->isUnreadByActiveUser;
    }

    /**
     *
     * @return \STCompany\Entity\UserCollection|null
     */
    public function getMentionedUsers(): ?\STCompany\Entity\UserCollection
    {
        return $this->mentionedUsers;
    }

    /**
     *
     * @return array
     */
    public function getMentionedUserIds(): array
    {
        $regex = '/' . static::MENTION_START_DELIMITER . '(\d+)' . static::MENTION_END_DELIMITER . '/';
        $matches = [];
        preg_match_all($regex, $this->getMessageBody(), $matches, PREG_PATTERN_ORDER);
        return array_map('intval', $matches[1] ?? []);
    }

    /**
     *
     * @return array
     */
    public function getNotifiedUserIds(): array
    {
        return $this->notifiedUserIds;
    }

    /**
     *
     * @return \Carbon\Carbon
     */
    public function getCreated(): \Carbon\Carbon
    {
        if (is_null($this->created)) {
            $this->created = \Carbon\Carbon::now();
        }
        return $this->created;
    }

    /**
     *
     * @param string|null $commentId
     * @return Comment
     */
    public function setCommentId(?string $commentId): Comment
    {
        if (is_null($commentId)) {
            $commentId = uniqid(static::COMMENT_ID_PREFIX, true);
        }
        $this->commentId = $commentId;
        return $this;
    }

    /**
     *
     * @param int $companyId
     * @return Comment
     */
    public function setCompanyId(int $companyId): Comment
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     *
     * @param int $userId
     * @return Comment
     */
    public function setUserId(int $userId): Comment
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\User $user
     * @return Comment
     */
    public function setUser(\STCompany\Entity\User $user): Comment
    {
        $this->user = $user;
        return $this;
    }

    /**
     *
     * @param string $callId
     * @return Comment
     */
    public function setCallId(string $callId): Comment
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param string $messageBody
     * @return Comment
     */
    public function setMessageBody(string $messageBody): Comment
    {
        $this->messageBody = $messageBody;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\UserCollection|null $mentionedUsers
     * @return Comment
     */
    public function setMentionedUsers(?\STCompany\Entity\UserCollection $mentionedUsers): Comment
    {
        $this->mentionedUsers = $mentionedUsers;
        return $this;
    }

    /**
     *
     * @param array $notifiedUserIds
     * @return Comment
     */
    public function setNotifiedUserIds(array $notifiedUserIds = []): Comment
    {
        $this->notifiedUserIds = $notifiedUserIds;
        return $this;
    }

    /**
     *
     * @param string|\Carbon\Carbon $created
     * @return Comment
     */
    public function setCreated(string|\Carbon\Carbon $created): Comment
    {
        $this->created = is_string($created) ? \Carbon\Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @param bool|null $isUnreadByActiveUser
     * @return Comment
     */
    public function setIsUnreadByActiveUser(?bool $isUnreadByActiveUser): Comment
    {
        $this->isUnreadByActiveUser = $isUnreadByActiveUser;
        return $this;
    }

    /**
     *
     * @param bool|null $isUnreadByActiveUser
     * @return null|bool|Comment
     */
    public function isUnreadByActiveUser(?bool $isUnreadByActiveUser): null|bool|Comment
    {
        if (is_null($isUnreadByActiveUser)) {
            return $this->isUnreadByActiveUser;
        }
        $this->isUnreadByActiveUser = $isUnreadByActiveUser;
        return $this;
    }

    /**
     *
     * @param int $userId
     * @return Comment
     */
    public function addNotifiedUserId(int $userId): Comment
    {
        $this->notifiedUserIds[] = $userId;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        if ($this->getUser() instanceof \STUser\Entity\User) {
            $result['user'] = $this->getUser()->toArray([
                'id',
                'name',
                'avatar',
                'personal_avatar_file',
            ]);
        } else {
            unset($result['user']);
        }
        unset($result['mentioned_users']);
        unset($result['notified_user_ids']);
        return $result;
    }
}
