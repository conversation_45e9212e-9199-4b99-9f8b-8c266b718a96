<?php

declare(strict_types=1);

namespace STCall\Entity;

/**
 *
 * @todo Create BaseCall. Call and ChatCall should be extended from BaseCall
 */
class ChatCall extends Call
{
    public const CHAT_CALL_TYPE = 'chat-call';
    protected const DEFAULT_DURATION = 0;
    protected const DEFAULT_TRANSCRIBING_DRIVER = 'none';

    public const AGENT_NUMBER = 0;
    public const CLIENT_NUMBER = 1;

    /**
     *
     * @return string|null
     */
    public function getOriginalFileName(): ?string
    {
        return null;
    }

    /**
     *
     * @return int|null
     */
    public function getDuration(): ?int
    {
        return static::DEFAULT_DURATION;
    }

    /**
     *
     * @return string|null
     */
    public function getS3FilePath(): ?string
    {
        return null;
    }

    /**
     *
     * @return string|null
     */
    public function getTranscribingDriver(): ?string
    {
        return static::DEFAULT_TRANSCRIBING_DRIVER;
    }

    /**
     *
     * @return bool
     */
    public function getIsTranscribed(): bool
    {
        return true;
    }

    /**
     *
     * @return bool
     */
    public function getIsRunManually(): bool
    {
        return false;
    }

    /**
     *
     * @return string
     */
    public function getCallType(): string
    {
        return static::CHAT_CALL_TYPE;
    }

    /**
     * @return \Carbon\Carbon|null
     */
    public function getTranscribedAt(): ?\Carbon\Carbon
    {
        return null;
    }

    /**
     *
     * @param string|null $originalFileName
     * @return Call
     */
    public function setOriginalFileName(?string $originalFileName): Call
    {
        return $this;
    }

    /**
     *
     * @param int|null $duration
     * @return Call
     */
    public function setDuration(?int $duration): Call
    {
        return $this;
    }

    /**
     *
     * @param string|null $filePath
     * @return Call
     */
    public function setFilePath(?string $filePath): Call
    {
        return $this;
    }

    /**
     *
     * @param string|null $s3FilePath
     * @return Call
     */
    public function setS3FilePath(?string $s3FilePath): Call
    {
        return $this;
    }

    /**
     *
     * @param string|null $transcribingDriver
     * @return Call
     */
    public function setTranscribingDriver(?string $transcribingDriver): Call
    {
        return $this;
    }

    /**
     *
     * @param bool $isTranscribed
     * @return Call
     */
    public function setIsTranscribed(bool $isTranscribed): Call
    {
        return $this;
    }

    /**
     *
     * @param bool $isRunManually
     * @return Call
     */
    public function setIsRunManually(bool $isRunManually): Call
    {
        return $this;
    }

    /**
     *
     * @param \Carbon\Carbon|string|null $transcribedAt
     * @return $this
     */
    public function setTranscribedAt(\Carbon\Carbon|string|null $transcribedAt): Call
    {
        return $this;
    }

    /**
     *
     * @param bool|null $isTranscribed
     * @return Call|bool
     */
    public function isTranscribed(?bool $isTranscribed = null): Call|bool
    {
        if (is_null($isTranscribed)) {
            return true;
        }
        return $this;
    }

    /**
     *
     * @param bool|null $isSentToTranscribing
     * @return bool|Call
     */
    public function isSentToTranscribing(bool $isSentToTranscribing = null): bool|Call
    {
        if (is_null($isSentToTranscribing)) {
            return true;
        }
        return $this;
    }
}
