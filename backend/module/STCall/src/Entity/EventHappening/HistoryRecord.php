<?php

declare(strict_types=1);

namespace STCall\Entity\EventHappening;

class HistoryRecord
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    public const ALGO_EVENT_AUTHOR = 'Robonote algorithm';
    public const SEARCH_WORD_AUTHOR = 'Users search word';

    /**
     *
     * @var \STCompany\Entity\Event\Event|null
     */
    protected ?\STCompany\Entity\Event\Event $event;

    /**
     *
     * @var int
     */
    protected string $author;

    /**
     *
     * @var float
     */
    protected float $score = 100;

    /**
     *
     * @var string|null
     */
    protected ?string $avatar = null;

    /**
     *
     * @var string|null
     */
    protected ?string $personalAvatarFile = null;

    /**
     *
     * @var bool
     */
    protected bool $isRobonoteAvatar = true;

    /**
     *
     * @var bool
     */
    protected bool $isEmptyIcon = false;

    /**
     *
     * @var \Carbon\Carbon
     */
    protected ?\Carbon\Carbon $created = null;

    /**
     *
     * @return \STCompany\Entity\Event\Event
     */
    public function getEvent(): ?\STCompany\Entity\Event\Event
    {
        return $this->event;
    }

    /**
     *
     * @return string
     */
    public function getAuthor(): string
    {
        return $this->author;
    }

    /**
     *
     * @return float
     */
    public function getScore(): float
    {
        return $this->score;
    }

    /**
     *
     * @return string|null
     */
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    /**
     *
     * @return string|null
     */
    public function getPersonalAvatarFile(): ?string
    {
        return $this->personalAvatarFile;
    }

    /**
     *
     * @return bool
     */
    public function getIsRobonoteAvatar(): bool
    {
        return $this->isRobonoteAvatar;
    }

    /**
     *
     * @return bool
     */
    public function getIsEmptyIcon(): bool
    {
        return $this->isEmptyIcon;
    }

    /**
     *
     * @return \Carbon\Carbon|null
     */
    public function getCreated(): ?\Carbon\Carbon
    {
        return $this->created;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event $event
     * @return HistoryRecord
     */
    public function setEvent(?\STCompany\Entity\Event\Event $event): HistoryRecord
    {
        $this->event = $event;
        return $this;
    }

    /**
     *
     * @param string $author
     * @return HistoryRecord
     */
    public function setAuthor(string $author): HistoryRecord
    {
        $this->author = $author;
        return $this;
    }

    /**
     *
     * @param float $score
     * @return HistoryRecord
     */
    public function setScore(float $score): HistoryRecord
    {
        $this->score = $score;
        return $this;
    }

    /**
     *
     * @param string|null $avatar
     * @return HistoryRecord
     */
    public function setAvatar(?string $avatar): HistoryRecord
    {
        $this->avatar = $avatar;
        return $this;
    }

    /**
     *
     * @param string|null $personalAvatarFile
     * @return HistoryRecord
     */
    public function setPersonalAvatarFile(?string $personalAvatarFile): HistoryRecord
    {
        $this->personalAvatarFile = $personalAvatarFile;
        return $this;
    }

    /**
     *
     * @param bool $isRobonoteAvatar
     * @return HistoryRecord
     */
    public function setIsRobonoteAvatar(bool $isRobonoteAvatar): HistoryRecord
    {
        $this->isRobonoteAvatar = $isRobonoteAvatar;
        return $this;
    }

    /**
     *
     * @param bool $isEmptyIcon
     * @return HistoryRecord
     */
    public function setIsEmptyIcon(bool $isEmptyIcon): HistoryRecord
    {
        $this->isEmptyIcon = $isEmptyIcon;
        return $this;
    }

    /**
     *
     * @param null|string|\Carbon\Carbon $created
     * @return HistoryRecord
     */
    public function setCreated(null|string|\Carbon\Carbon $created): HistoryRecord
    {
        $this->created = is_string($created) ? \Carbon\Carbon::parse($created) : $created;
        return $this;
    }

    /**
     *
     * @param bool|null $isRobonoteAvatar
     * @return bool|HistoryRecord
     */
    public function isRobonoteAvatar(?bool $isRobonoteAvatar = null): bool|HistoryRecord
    {
        if (is_bool($isRobonoteAvatar)) {
            $this->isRobonoteAvatar = $isRobonoteAvatar;
            return $this;
        }
        return $this->isRobonoteAvatar;
    }

    /**
     *
     * @param bool|null $isEmptyIcon
     * @return bool|HistoryRecord
     */
    public function isEmptyIcon(?bool $isEmptyIcon = null): bool|HistoryRecord
    {
        if (is_bool($isEmptyIcon)) {
            $this->isEmptyIcon = $isEmptyIcon;
            return $this;
        }
        return $this->isEmptyIcon;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['event'] = $this->getEvent()?->toArray();
        return $result;
    }
}
