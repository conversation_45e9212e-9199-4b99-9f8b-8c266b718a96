<?php

declare(strict_types=1);

namespace STCall\Entity;

use RuntimeException;
use STLib\Expand\Collection;
use Traversable;

/**
 * @extends Traversable<array-key,Paragraph>
 */
class ParagraphCollection extends Collection
{
    /**
     *
     * @param mixed $paragraph
     * @param string|int|null $key
     * @return Collection
     * @throws RuntimeException
     */
    public function add(mixed $paragraph, string|int|null $key = null): Collection
    {
        if (!($paragraph instanceof Paragraph)) {
            throw new RuntimeException('Paragraph must be an instace of "\STCall\Entity\Paragraph"');
        }
        parent::add($paragraph, $key ?? $paragraph->getParagraphNumber());
        return $this;
    }

    /**
     *
     * @param float $startTime
     * @param float $endTime
     * @return Paragraph|null
     */
    public function getParagraphNumberByTimestamp(float $startTime, float $endTime): ?Paragraph
    {
        $searchedParagraph = null;
        $searchedParagraphLength = 0;
        foreach ($this as $paragraph) {
            if (
                    ($startTime <= $paragraph->getStartTime() && $endTime >= $paragraph->getEndTime())
                    || ($startTime >= $paragraph->getStartTime() && $startTime <= $paragraph->getEndTime())
                    || ($endTime >= $paragraph->getStartTime() && $endTime <= $paragraph->getEndTime())
            ) {
                $length = min($endTime, $paragraph->getEndTime()) - max($startTime, $paragraph->getStartTime());
                if ($length > $searchedParagraphLength) {
                    $searchedParagraphLength = $length;
                    $searchedParagraph = $paragraph;
                }
            }
        }
        return $searchedParagraph;
    }

    /**
     *
     * @param array $attributes
     * @return array
     */
    public function toArray(array $attributes = []): array
    {
        $result = [];
        foreach ($this as $paragraph) {
            $result[] = $paragraph->toArray($attributes);
        }
        return $result;
    }
}
