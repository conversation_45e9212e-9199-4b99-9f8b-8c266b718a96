<?php

declare(strict_types=1);

namespace STCall\Entity;

class CallRoleCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $callRole
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $callRole, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($callRole instanceof CallRole)) {
            throw new \RuntimeException('Call must be an instace of "\STCall\Entity\CallRole"');
        }
        parent::add($callRole, $key ?? $callRole->getCall()->getId() . '-' . $callRole->getRoleId());
        return $this;
    }
}
