<?php

declare(strict_types=1);

namespace STLog\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;

class Logger extends AbstractPlugin
{
    /**
     *
     * @param string $name
     * @return \Laminas\Log\Logger
     */
    public function __invoke(string $name): \Laminas\Log\Logger
    {
        $logManager = $this->getController()->getServiceManager()->get(\STLog\Service\LogManager::class);
        return $logManager->create($name);
    }
}
