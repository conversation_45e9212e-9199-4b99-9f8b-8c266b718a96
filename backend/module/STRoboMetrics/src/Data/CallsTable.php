<?php

declare(strict_types=1);

namespace STRoboMetrics\Data;

use Carbon\Carbon;
use STCall\Entity\Call;
use STCall\Entity\ChatCall;
use STRoboMetrics\Request\Call\SummaryRequest;

class CallsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;

    /**
     *
     * @param \STRoboMetrics\Request\Call\CallLanguagesRequest $request
     * @return array
     */
    public function getCallHoursPerLanguages(\STRoboMetrics\Request\Call\CallLanguagesRequest $request): array
    {
        $sql = <<<SQL
            SELECT
                call_language,
                round(SUM(call_duration) / 60 / 60) hours
            FROM ({$this->getFinalTableSqlUsingGroupBy(
            'calls',
            [
                'company_id',
                'call_id',
            ],
            'created',
            [
                'call_duration',
                'call_language',
            ],
            [
                'company_id' => $request->getCompanyId(),
                [
                    'column' => 'call_time',
                    'value' => $request->getStartDate(),
                    'type' => 'date',
                    'compare' => '>=',
                ],
                [
                    'column' => 'call_time',
                    'value' => $request->getEndDate(),
                    'type' => 'date',
                    'compare' => '<=',
                ],
            ]
        )}) calls
            GROUP BY
                call_language
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param \STRoboMetrics\Request\Company\CompaniesCallsVolumeRequest $request
     * @return array
     */
    public function getCompaniesCallsVolume(\STRoboMetrics\Request\Company\CompaniesCallsVolumeRequest $request): array
    {
        $sql = <<<SQL
            SELECT
                last_value_respect_nulls(companies.company_name) company_name,
                toDate(call_time) call_date,
                toInt32(count(call_id)) calls_count
            FROM
                ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'call_duration',
                        'call_time'
                    ],
                    [
                        [
                            'column' => 'call_time',
                            'value' => $request->getStartDate(),
                            'type' => 'date',
                            'compare' => '>=',
                        ],
                        [
                            'column' => 'call_time',
                            'value' => $request->getEndDate(),
                            'type' => 'date',
                            'compare' => '<=',
                        ],
                    ]
                )
            }) calls
            INNER JOIN dictionary(companies) companies ON calls.company_id = companies.company_id
            GROUP BY
                company_id,
                toDate(call_time)
            ORDER BY company_name
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     * @return array
     */
    public function getCompaniesWithLowerCallsVolume(): array
    {
        $sql = <<<SQL
            SELECT
                avg(calls_avg.calls_count) avg_calls_count,
                last_value_respect_nulls(cpd.calls_count) previous_day_calls_count,
                company_id
            FROM (
                SELECT
                    company_id,
                    uniq(call_id) calls_count
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'call_duration',
                        'call_time'
                    ],
                    [
                        [
                            'column' => 'call_time',
                            'value' => (new Carbon())->subDays(3)->startOfDay(),
                            'type' => 'date',
                            'compare' => '>=',
                        ],
                        [
                            'column' => 'call_time',
                            'value' => (new Carbon())->subDay()->startOfDay(),
                            'type' => 'date',
                            'compare' => '<',
                        ],
                    ]
                )})
                GROUP BY company_id
            ) calls_avg
            LEFT JOIN (
                SELECT
                    company_id,
                    uniq(call_id) calls_count
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'call_duration',
                        'call_time'
                    ],
                    [
                        [
                            'column' => 'call_time',
                            'value' => (new Carbon())->subDay()->startOfDay(),
                            'type' => 'date',
                            'compare' => '>=',
                        ],
                        [
                            'column' => 'call_time',
                            'value' => (new Carbon())->subDay()->endOfDay(),
                            'type' => 'date',
                            'compare' => '<=',
                        ],
                    ]
                )})
                GROUP BY company_id
            ) cpd ON cpd.company_id = calls_avg.company_id
            GROUP BY
                company_id
            HAVING
                previous_day_calls_count < round(avg_calls_count / 100 * 30)
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    /**
     *
     * @param SummaryRequest $summaryRequest
     * @return array
     */
    public function getCallsSummary(SummaryRequest $summaryRequest): array
    {
        $select = [
            'ROUND(SUM(call_duration) / 3600) hours',
            'COUNT(*) calls_count',
            'ROUND(SUMIf(call_duration, is_analyzed = 1) / 3600) analyzed_hours',
            'COUNTIf(is_transcribed = 1) analyzed_calls_count',
            'analyzed_calls_count / calls_count * 100 analyzed_count_percent',
            'analyzed_hours / hours * 100 analyzed_hours_percent',
        ];
        return $this->executeSummaryQuery($summaryRequest, $select);
    }

    /**
     *
     * @param SummaryRequest $summaryRequest
     * @return array
     */
    public function getChatCallsSummary(SummaryRequest $summaryRequest): array
    {
        $select = [
            'COUNT(*) chats_count',
            'COUNTIf(is_analyzed = 1) analyzed_chats_count',
            'analyzed_chats_count / chats_count * 100 analyzed_count_percent',
        ];

        return $this->executeSummaryQuery($summaryRequest, $select);
    }

    /**
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getBillingSummary(Carbon $startDate, Carbon $endDate): array
    {
        $sql = <<<SQL
            SELECT
                calls.analyzed_hours analyzed_hours,
                calls.period period,
                companies.company_id company_id,
                companies.company_name company_name,
                rates.rate rate,
                rate * analyzed_hours billing_sum
            FROM (
                SELECT
                    ROUND(SUMIf(call_duration, is_analyzed = 1) / 3600) analyzed_hours,
                    concat(toYear(transcribed_at), '-', toMonth(transcribed_at)) period,
                    company_id
                FROM ({$this->getFinalTableSqlUsingGroupBy(
                    'calls',
                    [
                        'company_id',
                        'call_id',
                    ],
                    'created',
                    [
                        'call_duration',
                        'transcribed_at',
                        'is_analyzed'
                    ],
                    [
                        [
                            'column' => 'transcribed_at',
                            'value' => $startDate,
                            'type' => 'date',
                            'compare' => '>=',
                        ],
                        [
                            'column' => 'transcribed_at',
                            'value' => $endDate,
                            'type' => 'date',
                            'compare' => '<',
                        ],
                    ]
                )})
                GROUP BY period, company_id
            ) calls
            INNER JOIN (
                SELECT
                    company_id,
                    company_name
                FROM dictionary(companies)
                ) companies ON companies.company_id = calls.company_id
            LEFT JOIN (
                SELECT
                    company_id,
                    rate
                FROM dictionary(companies_rates)
            ) rates ON rates.company_id = companies.company_id
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    public function getCallsStatistics(Carbon $startDate, Carbon $endDate): ?array
    {
        $sql = <<<SQL
            SELECT
                company_name,
                ROUND(SUM(call_duration) / 3600, 2) as total_call_hours,
                ROUND(SUM(IF(is_analyzed = 1, call_duration, 0)) / 3600, 2) as total_analyzed_hours
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_duration',
                    'call_time',
                    'call_type',
                    'is_analyzed'
                ],
                [
                    [
                        'column' => 'call_time',
                        'value' => $startDate,
                        'type' => 'date',
                        'compare' => '>=',
                    ],
                    [
                        'column' => 'call_time',
                        'value' => $endDate,
                        'type' => 'date',
                        'compare' => '<=',
                    ],
                    'call_type' => Call::CALL_TYPE,
                ]
            )}) calls
            INNER JOIN dictionary(companies) companies ON calls.company_id = companies.company_id
            GROUP BY
                companies.company_id,
                companies.company_name
            ORDER BY
                companies.company_name
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    public function getChatStatistics(Carbon $startDate, Carbon $endDate): ?array
    {
        $sql = <<<SQL
            SELECT
                companies.company_name as company_name,
                COUNT(*) as total_chats_count
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'calls',
                [
                    'company_id',
                    'call_id',
                ],
                'created',
                [
                    'call_time',
                    'call_type'
                ],
                [
                    [
                        'column' => 'call_time',
                        'value' => $startDate,
                        'type' => 'date',
                        'compare' => '>=',
                    ],
                    [
                        'column' => 'call_time',
                        'value' => $endDate,
                        'type' => 'date',
                        'compare' => '<=',
                    ],
                    'call_type' => ChatCall::CHAT_CALL_TYPE,
                ]
            )}) calls
            INNER JOIN dictionary(companies) companies ON calls.company_id = companies.company_id
            GROUP BY
                companies.company_id,
                companies.company_name
        SQL;

        return $this->getClient()->selectAll($sql);
    }

    public function getChecklistsStatistics(Carbon $startDate, Carbon $endDate): ?array
    {
        $sql = <<<SQL
            SELECT
                subquery.company_id as company_id,
                last_value_respect_nulls(subquery.company_name) as company_name,
                groupArray(
                    map(
                        'checklist_name', toString(checklist_name),
                        'total_checklist_conversations', toString(total_checklist_conversations),
                        'billing_units', toString(billing_units)
                    )
                ) as checklists
            FROM (
                SELECT
                    companies.company_id as company_id,
                    companies.company_name as company_name,
                    last_value_respect_nulls(cp.checklist_name) as checklist_name,
                    COUNT(DISTINCT ccp.call_id) as total_checklist_conversations,
                    CASE
                        WHEN total_checklist_conversations < 10000 THEN 1
                        WHEN total_checklist_conversations <= 30000 THEN 2
                        ELSE 4
                    END as billing_units
                FROM calls_checklists_points ccp
                INNER JOIN dictionary(companies) companies ON ccp.company_id = companies.company_id
                INNER JOIN dictionary(checklists_points) cp ON ccp.checklist_point_id = cp.checklist_point_id
                WHERE
                    ccp.call_time >= :start_date
                    AND ccp.call_time <= :end_date
                GROUP BY
                    companies.company_id,
                    companies.company_name,
                    cp.checklist_id
            ) subquery
            GROUP BY
                company_id
        SQL;

        $params = [
            'start_date' => $startDate->format('Y-m-d H:i:s'),
            'end_date' => $endDate->format('Y-m-d H:i:s'),
        ];

        return $this->getClient()->selectAll($sql, $params);
    }

    public function getSummarizationStatistics(Carbon $startDate, Carbon $endDate): ?array
    {
        $sql = <<<SQL
            SELECT
                companies.company_name as company_name,
                COUNT(DISTINCT call_id) as total_summarization_conversations
            FROM
                calls_summarizations cs
            INNER JOIN dictionary(companies) companies ON cs.company_id = companies.company_id
            WHERE
                created_at >= :start_date
                AND created_at <= :end_date
            GROUP BY
                companies.company_id,
                companies.company_name
        SQL;

        $params = [
            'start_date' => $startDate->format('Y-m-d H:i:s'),
            'end_date' => $endDate->format('Y-m-d H:i:s'),
        ];

        return $this->getClient()->selectAll($sql, bindings: $params);
    }

    protected function executeSummaryQuery(SummaryRequest $summaryRequest, array $select): ?array
    {
        $where = [
            $summaryRequest->getCallDatetimeParamDimension() . ' >= toDateTime(\'' . $summaryRequest->getStartDate() . '\')',
            $summaryRequest->getCallDatetimeParamDimension() . ' <= toDateTime(\'' . $summaryRequest->getEndDate() . '\')',
            'call_type = \'' . $summaryRequest->getCallType() . '\'',
        ];
        $groupBy = [];

        if ($summaryRequest->getTimeDimension() !== SummaryRequest::TIME_DIMENSIONS_ALL) {
            $select[] = 'formatDateTime(' . $summaryRequest->getCallDatetimeParamDimension() . ', \'' . $summaryRequest->getTimeDimensionFormat() . '\') period';
            $groupBy[] = 'period';
        }
        if ($summaryRequest->isUsingCompanyDimension()) {
            $select[] = 'company_id';
            $groupBy[] = 'company_id';
        }
        if (is_int($summaryRequest->getCompanyId()) && $summaryRequest->getCompanyId() > 0) {
            $where[] = 'company_id = ' . $summaryRequest->getCompanyId();
        }

        $sql = '
            SELECT
        ';
        if ($summaryRequest->getIsUsingCompanyDimension()) {
            $sql .= '
                com.company_name,
            ';
        }
        $sql .= '
                c.*
        ';
        $sql .= '
            FROM
                (
                    SELECT
                        ' . implode(',', $select) . '
                    FROM
                        calls FINAL
                    WHERE
                        ' . implode(' AND ', $where) . '
        ';
        if (count($groupBy) > 0) {
            $sql .= '
                    GROUP BY
                        ' . implode(',', $groupBy) . '
            ';
        }
        $sql .= '
                ) c
        ';
        if ($summaryRequest->getIsUsingCompanyDimension()) {
            $sql .= '
            INNER JOIN
                dictionary(companies) com
                ON com.company_id = c.company_id
            ';
        }
        if (in_array('period', $groupBy)) {
            $sql .= '
            ORDER BY
                period DESC
            ';
        }

        return $this->getClient()->selectAll($sql);
    }
}
