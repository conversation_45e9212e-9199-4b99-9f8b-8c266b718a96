<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

class DictionaryController extends BaseController
{
    /**
     *
     * @return array
     */
    public function getCompaniesAction(): array
    {
        return $this->roboMetrics()->dictionary()->getCompaniesNames();
    }

    /**
     *
     * @return array
     */
    public function getAlgoEventsAction(): array
    {
        return $this->roboMetrics()->dictionary()->getAlgoEventsNames();
    }
}
