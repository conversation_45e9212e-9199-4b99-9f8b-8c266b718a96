<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCompany\Validator\Industry\ConnectIndustryValidator;
use STCompany\Validator\Industry\DisconnectIndustryValidator;

final class CompanyIndustryController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getIndustriesAction(): array
    {
        return [
            'industries' => $this->company()->industrySelector()->getIndustries($this->company->getId())->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function connectIndustryAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var ConnectIndustryValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectIndustryValidator::class);
        $validator->setInstance(['industry_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $industry = $this->company()->industryConnector()->connect($id, $this->company->getId());

        return [
            'industry' => $industry->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectIndustryAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var DisconnectIndustryValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectIndustryValidator::class);
        $validator->setInstance(['industry_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->industryConnector()->disconnect($id, $this->company->getId());

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected industry from company.',
        ];
    }
}
