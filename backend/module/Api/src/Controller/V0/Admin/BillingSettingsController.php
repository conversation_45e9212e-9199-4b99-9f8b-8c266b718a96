<?php

declare(strict_types=1);

namespace Api\Controller\V0\Admin;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCompany\Validator\BillingSettings\CreateBillingSettingsValidator;
use STCompany\Validator\BillingSettings\UpdateBillingSettingsValidator;

class BillingSettingsController extends BaseController
{
    /**
     * @return array
     * @throws NotFoundApiException
     * @throws ReflectionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function createAction(): array
    {
        $companyId = (int) $this->getApiParam('id');
        $this->company()->getCompany($companyId, $this->auth()->getUser()->getId());

        $duration = $this->getApiParam('duration');
        $callsSeconds = $this->getApiParam('calls_seconds');
        $chatsAmount = $this->getApiParam('chats_amount');
        $summarizationsAmount = $this->getApiParam('summarizations_amount');
        $checklistsAmount = $this->getApiParam('checklists_amount');
        $eachChecklistsCallsAmount = $this->getApiParam('each_checklists_calls_amount');

        /** @var CreateBillingSettingsValidator $validator */
        $validator = $this->getServiceManager()->get(CreateBillingSettingsValidator::class);
        $validator->setInstance([
            'company_id' => $companyId,
            'duration' => $duration,
            'calls_seconds' => $callsSeconds,
            'chats_amount' => $chatsAmount,
            'summarizations_amount' => $summarizationsAmount,
            'checklists_amount' => $checklistsAmount,
            'each_checklists_calls_amount' => $eachChecklistsCallsAmount
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $billingSettings = $this->company()->billingSettingsSaver()->create(
            $companyId,
            (int) $duration,
            (int) $callsSeconds,
            (int) $chatsAmount,
            (int) $summarizationsAmount,
            (int) $checklistsAmount,
            (int) $eachChecklistsCallsAmount
        );

        return [
            'billing-settings' => $billingSettings->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ReflectionException
     * @throws ValidationApiException
     */
    public function updateAction(): array
    {
        $companyId = (int) $this->getApiParam('id');
        $this->company()->getCompany($companyId, $this->auth()->getUser()->getId());

        $duration = $this->getApiParam('duration');
        $callsSeconds = $this->getApiParam('calls_seconds');
        $chatsAmount = $this->getApiParam('chats_amount');
        $summarizationsAmount = $this->getApiParam('summarizations_amount');
        $checklistsAmount = $this->getApiParam('checklists_amount');
        $eachChecklistsCallsAmount = $this->getApiParam('each_checklists_calls_amount');

        /** @var UpdateBillingSettingsValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateBillingSettingsValidator::class);
        $validator->setInstance([
            'company_id' => $companyId,
            'duration' => $duration,
            'calls_seconds' => $callsSeconds,
            'chats_amount' => $chatsAmount,
            'summarizations_amount' => $summarizationsAmount,
            'checklists_amount' => $checklistsAmount,
            'each_checklists_calls_amount' => $eachChecklistsCallsAmount
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->billingSettingsSaver()->update(
            $companyId,
            (int) $duration,
            (int) $callsSeconds,
            (int) $chatsAmount,
            (int) $summarizationsAmount,
            (int) $checklistsAmount,
            (int) $eachChecklistsCallsAmount
        );

        return [
            'billing-settings' => $this->company()->billingSettingsSelector()->getBillingSettings($companyId)->toArray(),
        ];
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function getAction(): array
    {
        $companyId = (int) $this->getApiParam('id');
        $this->company()->getCompany($companyId, $this->auth()->getUser()->getId());

        return [
            'billing-settings' => $this->company()->billingSettingsSelector()->getBillingSettings($companyId)->toArray(),
        ];
    }
}
