<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCompany\Validator\AlgoApi\ConnectAlgoApiValidator;
use STCompany\Validator\AlgoApi\DisconnectAlgoApiValidator;

final class CompanyAlgoApiController extends BaseController
{
    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function connectAlgoApiAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var ConnectAlgoApiValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectAlgoApiValidator::class);
        $validator->setInstance(['algo_api_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $algoApi = $this->company()->algoApiConnector()->connect($id, $this->company->getId());

        return [
            'algo_api' => $algoApi->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectAlgoApiAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var DisconnectAlgoApiValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectAlgoApiValidator::class);
        $validator->setInstance(['algo_api_id' => $id, 'company_id' => $this->company->getId()]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->algoApiConnector()->disconnect($id, $this->company->getId());

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected algo api from company.',
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     */
    public function getAlgoApisAction(): array
    {
        return [
            'algo_apis' => $this->algo()->companyAlgoApiSelector()->getNerAlgoApis($this->company->getId())->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws ReflectionException
     * @throws NotFoundExceptionInterface
     */
    public function getDirectNerAlgoApisAction(): array
    {
        return [
            'algo_apis' => $this->company()->algoApiSelector()->getDirectNerAlgoApis($this->company->getId())->toArray(),
        ];
    }
}
