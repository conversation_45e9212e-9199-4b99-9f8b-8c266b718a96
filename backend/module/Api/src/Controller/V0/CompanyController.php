<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use STApi\Entity\Exception\NotFoundApiException;
use Carbon\Carbon;
use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyDetails;
use STCompany\Validator\CompanyDetailsValidator;
use STCompany\Validator\CompanyValidator;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STReport\Entity\CountriesConverter;

class CompanyController extends BaseController
{
    use BaseHydratorTrait;
    use CountriesConverter;

    protected const CLIENT_NUMBER_MAX_LIMIT = 100;
    protected const CLIENT_NUMBER_MIN_LIMIT = 1;

    /**
     *
     *
     * @return array
     */
    public function getUserAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        return [
            'user' => $user->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyAction(): array
    {
        return [
            'company' => $this->company->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyStructureAction(): array
    {
        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());
        return [
            'teams' => $this->company()->getCompanyStructure($this->company->getId(), $teamIds),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyClientsAction(): array
    {
        $clientSearch = $this->getApiParam('id') ?? $this->getApiParam('name') ?? '';

        $limit = (int) $this->getApiParam('limit');

        if ($limit > static::CLIENT_NUMBER_MAX_LIMIT || $limit < static::CLIENT_NUMBER_MIN_LIMIT) {
            $limit = static::CLIENT_NUMBER_MAX_LIMIT;
        }

        $teamIds = $this->company()->user()->getUserTeamIds($this->company->getId(), $this->auth()->getUser()->getId());

        return [
            'clients' => $this->company()->getCompanyClients($this->company->getId(), $clientSearch, $limit, $teamIds),
        ];
    }

    public function getCompanyDetailsAction(): array
    {
        $companyDetails = $this->company()->getCompanyDetails($this->company->getId());

        return [
            'companyDetails' => $companyDetails->toArray(),
        ];
    }

    public function getCompanyApiTokenAction()
    {
        return [
            'api_token' => $this->api()->application()->getApplicationByCompanyId($this->company->getId())?->getToken(),
        ];
    }

    public function saveCompanyDetailsAction(): array
    {
        $existingCompanyDetails = $this->company()->getCompanyDetails($this->company->getId());
        if ($existingCompanyDetails->getCompanyId() === null) {
            $newCompanyDetails = true;
        } else {
            $newCompanyDetails = false;
        }

        /** @var CompanyDetails $companyDetails */
        $companyDetails = $this->hydrate($this->getApiParams()->toArray(), CompanyDetails::class);
        $companyDetails->setCompanyId($this->company->getId());

        $validator = $this->getServiceManager()->get(CompanyDetailsValidator::class);
        $validator->setInstance($companyDetails);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        if ($newCompanyDetails) {
            $this->company()->saveCompanyDetails($companyDetails);
        } else {
            $this->company()->updateCompanyDetails($companyDetails);
        }

        return [
            'companyDetails' => $companyDetails->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyCallsStatusesAction(): array
    {
        return [
            'statuses' => $this->call()->getCallStatusesByCompany($this->company),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyCallsLanguagesAction(): array
    {
        return [
            'languages' => $this->call()->getCallLanguagesByCompany($this->company),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyClientsStatusesAction(): array
    {
        return [
            'statuses' => $this->company()->client()->getClientStatusesByCompany($this->company),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyClientsCountriesAction(): array
    {
        $countries = $this->company()->client()->getClientCountriesByCompany($this->company);

        return [
            'countries' => $countries,
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyClientsSourcesAction(): array
    {
        return [
            'sources' => $this->company()->client()->getClientSourcesByCompany($this->company),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCompanyClientsCampaignIdsAction(): array
    {
        return [
            'campaign_ids' => $this->company()->client()->getClientCampaignIdsByCompany($this->company),
        ];
    }

    /**
     * @return array
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     * @throws NotFoundApiException
     * @throws Exception
     */
    public function saveCompanyAction(): array
    {
        if ($this->getRequest()->isPut()) {
            $company = clone $this->company;
            $company
                    ->setName($this->getApiParam('name'))
                    ->setThresholdBar($this->getApiParam('threshold_bar'))
                    ->setMinCallDurationForAutoAnalyze($this->getApiParam('min_call_duration_for_auto_analyze') ?? null)
                    ->setLanguages($this->getApiParam('languages') ?? []);
            if ($this->hasApiParam('avatar') && !empty($this->getApiParam('avatar'))) {
                $company->setAvatar($this->getApiParam('avatar'));
            }
        } elseif ($this->getRequest()->isPost()) {
            /**
             * @var Company $company
             */
            $company = $this->hydrate($this->getApiParams()->toArray(), Company::class);
            $company->setStatus(Company::STATUS_ACTIVE);
            $company->setCreatedAt(new Carbon());
            $company->initStartBalance();
        }

        $validator = $this->getServiceManager()->get(CompanyValidator::class);
        $validator->setInstance($company);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->saveCompany($company);

        if ($this->getRequest()->isPost()) {
            $companyService = $this->company();
            $company->setAwsS3BucketRegion(\STCompany\Service\CompanyService::DEFAULT_AWS_REGION);
            $company->setAwsS3BucketName($companyService->getCommonAwsBucket($company));
            $company->setAwsS3BucketDir($companyService->createCompanyAwsBucketDir($company));

            $defaultApiIds = $this->algo()->api()->getDefaultApiIds();
            $this->algo()->api()->saveCompanyAlgoApiIds($company->getId(), $defaultApiIds);

            // save default roles
            $permissions = $this->company()->permission()->getPermissions();
            $roleService = $this->company()->role();
            $defaultRoles = $roleService->saveDefaultRolesToNewCompany($company, $permissions);
            $hiddenAdminDefaultRoleId = array_reduce($defaultRoles, function ($hiddenAdminRoleId, $defaultRole) {
                if ($defaultRole->isAdmin()) {
                    $hiddenAdminRoleId = $defaultRole->getId();
                }
                return $hiddenAdminRoleId;
            });

            // add global admin users as hidden admins
            $globalAdminUsers = $this->user()->getGlobalAdminUsers();
            $globalAdminUserIds = array_column($globalAdminUsers->toArray(), 'id');
            $roleService->bulkSaveUsers($globalAdminUserIds, $hiddenAdminDefaultRoleId, $company->getId());

            // add active front global admins
            $activeFrontGlobalAdmins = $this->user()->getFrontGlobalAdminUsers($this->front()->getActiveFront()->getId());
            $activeFrontGlobalAdminsUserIds = array_column($activeFrontGlobalAdmins->toArray(), 'id');
            $roleService->bulkSaveUsers($activeFrontGlobalAdminsUserIds, $hiddenAdminDefaultRoleId, $company->getId());

            $applicationService = $this->api()->application();
            $applicationService->generateApplication($company);

            $this->company()->saveCompany($company);
        } elseif ($this->getRequest()->isPut()) {
            $agentIds = $this->getApiParam('auto_analyzed_user_ids') ?? [];
            $userCompanyService = $this->company()->user();
            $userCompanyService->setAutoAnalyzeForUsers($this->company, $agentIds);
        }

        $this->company()->avatar()->deleteAvatar($company);

        return [
            'company' => $this->company()->getCompany($company->getId())->toArray(),
        ];
    }
}
