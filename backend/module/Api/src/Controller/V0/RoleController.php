<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use STApi\Entity\Exception\BadRequestApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCompany\Entity\Permission\UserPermission;
use STCompany\Entity\Role;
use STCompany\Validator\RoleValidator;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class RoleController extends BaseController
{
    use BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getRolesAction(): array
    {
        return [
            'roles' => $this->company()->role()->getRoles($this->company->getId(), [
                Role::COMPANY_ADMIN_ROLE_TYPE,
                Role::COMPANY_SUPERVISOR_ROLE_TYPE,
                Role::MANAGER_ROLE_TYPE,
                Role::AGENT_ROLE_TYPE,
            ])->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getRoleAction(): array
    {
        $roleId = (int) $this->getApiParam('role_id');
        return [
            'role' => $this->company()->role()->getRole($this->company->getId(), $roleId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException|ReflectionException
     */
    public function saveRoleAction(): array
    {
        $roleId = (int) $this->getApiParam('role_id');
        /** @var Role $role */
        $role = $this->hydrate([
            'name' => $this->getApiParam('name'),
            'company_id' => $this->company->getId(),
            'effective_call_threshold_bar' => (int) $this->getApiParam(
                'effective_call_threshold_bar',
                Role::DEFAULT_EFFECTIVE_CALL_THRESHOLD_BAR
            ),
        ], Role::class, withConstructor: true);

        foreach ($this->getApiParam('permissions') ?? [] as $permission) {
            $role->getPermissions()->add($this->hydrate($permission, UserPermission::class));
        }

        if ($roleId > 0) {
            // call to check access to role
            $existedRole = $this->company()->role()->getRole($this->company->getId(), $roleId);
            $role
                ->setId($roleId)
                ->setRoleType((int) ($this->getApiParam('role_type', $existedRole->getRoleType())));
        } else {
            $role->setRoleType((int) ($this->getApiParam('role_type', Role::MANAGER_ROLE_TYPE)));
        }

        $validator = $this->getServiceManager()->get(RoleValidator::class);
        $validator->setInstance($role);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $roleId = $this->company()->role()->saveRole($role);

        return [
            'role' => $this->company()->role()->getRole($this->company->getId(), $roleId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \Exception
     */
    public function bulkSaveUsersAction(): array
    {
        $userIds = (array) $this->getApiParam('user_ids');
        $roleId = (int) $this->getApiParam('role_id');

        //check user ids
        $companyUserIds = $this->company()->user()->filterCompanyUsers($this->company->getId(), $userIds);

        //check role id
        $this->company()->role()->getRole($this->company->getId(), $roleId);

        $this->company()->role()->bulkSaveUsers($companyUserIds, $roleId, $this->company->getId());

        return [
            'role' => $this->company()->role()->getRole($this->company->getId(), $roleId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws BadRequestApiException
     */
    public function deleteRoleAction(): array
    {
        $roleId = (int) $this->getApiParam('role_id');
        // call to check access to role
        $role = $this->company()->role()->getRole($this->company->getId(), $roleId);
        if (!$role->isManager()) {
            throw new BadRequestApiException('System roles can not be deleted');
        }
        $this->company()->role()->deleteRole($this->company->getId(), $role->getId());
        return [
            'deleted' => true,
        ];
    }
}
