<?php

declare(strict_types=1);

namespace Api\Controller\V0\Onboarding;

use Api\Controller\V0\BaseController;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use <PERSON><PERSON><PERSON>\Entity\Exception\NotFoundApiException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STOnboarding\Validator\UpdateUsersValidator;

class OnboardingUsersController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ValidationApiException
     */
    public function updateUsersAction(): array
    {
        $externalId = $this->getApiParam('id');
        $users = $this->getApiParam('users');

        /** @var UpdateUsersValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateUsersValidator::class);
        $validator->setInstance([
            'external_id' => $externalId,
            'users' => $users
        ]);
        $this->validate($validator);

        $this->onboarding()->onboardingFormSaver()->updateUsers($externalId, $users);

        return [
            'form' => $this->onboarding()->onboardingFormSelector()->getFormByExternalId($externalId)->toArray(),
        ];
    }
}
