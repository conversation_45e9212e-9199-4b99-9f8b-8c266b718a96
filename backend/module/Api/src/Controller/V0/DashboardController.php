<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Laminas\Http\Response;
use STApi\Entity\Exception\BadRequestApiException;
use STApi\Entity\Exception\InvalidResponseFormatApiException;
use STLib\Expand\CsvConverter;

class DashboardController extends \Api\Controller\V0\BaseController
{
    use CsvConverter;

    /**
     *
     * @return array
     */
    public function callsStatisticsAction(): array
    {
        return [
            'calls_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getCallsStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function managersKPIAction(): array
    {
        return [
            'managers_kpi' =>
                $this->dashboard()->dashboardStatisticsService()->getMangersKPIStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return Response
     * @throws InvalidResponseFormatApiException
     */
    public function managersKPICsvAction(): Response
    {
        $result = $this->dashboard()->dashboardStatisticsService()->getMangersKPIStatisticsForCsvReport(
            ...$this->getDashboardFiltersFromRequest()
        );

        return $this->output(
            $this->convertArrayToCsv($result),
            Response::STATUS_CODE_200,
            static::CSV_FORMAT,
            [
                'fileName' => 'managers_kpi_' . date('Y-m-d'),
            ],
        );
    }

    /**
     *
     * @return array
     */
    public function detailedManagersKPIAction(): array
    {
        return [
            'detailed_managers_kpi' =>
                $this->dashboard()->dashboardStatisticsService()->getDetailedManagersKPIStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function reviewedStatisticsAction(): array
    {
        return [
            'reviewed_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getReviewedStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function detailedReviewedStatisticsAction(): array
    {
        return [
            'detailed_reviewed_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getDetailedReviewedStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     * @throws BadRequestApiException
     */
    public function reviewedEventsStatisticsAction(): array
    {
        $categoryId = (int) $this->getApiParam('category_id');

        if ($categoryId <= 0) {
            throw new BadRequestApiException('category_id param is not provided');
        }

        $filters = array_merge(
            $this->getDashboardFiltersFromRequest(),
            ['categoryId' => $categoryId],
        );

        return [
            'reviewed_events_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getReviewedEventsStatistics(
                    ...$filters
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function eventsCategoriesStatisticsAction(): array
    {
        return [
            'events_categories_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getEventsCategoriesStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     * @throws BadRequestApiException
     */
    public function detailedEventsCategoriesStatisticsAction(): array
    {
        $categoryId = (int) $this->getApiParam('category_id');

        if ($categoryId <= 0) {
            throw new BadRequestApiException('category_id param is not provided');
        }

        $filters = array_merge(
            $this->getDashboardFiltersFromRequest(),
            ['categoryId' => $categoryId],
        );

        return [
            'detailed_events_categories_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getDetailedEventsCategoriesStatistics(
                    ...$filters
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function reviewedClientsStatisticsAction(): array
    {
        return [
            'reviewed_clients_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->getReviewedClientsStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     *
     * @return array
     */
    public function callsLanguagesStatisticsAction(): array
    {
        return [
            'calls_languages_statistics' =>
                $this->dashboard()->dashboardStatisticsService()->callsLanguagesStatistics(
                    ...$this->getDashboardFiltersFromRequest()
                ),
        ];
    }

    /**
     * @return array
     */
    private function getDashboardFiltersFromRequest(): array
    {
        return [
            'companyId' => (int) $this->company->getId(),
            'roleId' => $this->getApiParam('role_id') ? (int) $this->getApiParam('role_id') : null,
            'startDate' => $this->getApiParam('start_date'),
            'endDate' => $this->getApiParam('end_date'),
            'teamsIds' => $this->hasApiParam('teams_ids') ? (array) $this->getApiParam('teams_ids') : [],
        ];
    }
}
