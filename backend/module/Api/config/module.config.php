<?php

declare(strict_types=1);

namespace Api;

use Api\Controller\V0\WebhookController;
use STLib\Mvc\Controller\ControllerFactory;

return [
    'controllers' => [
        'factories' => [
            Controller\V0\Admin\CompanyController::class => ControllerFactory::class,
            Controller\V0\Admin\OnboardingFormController::class => ControllerFactory::class,
            Controller\V0\Admin\GeneralSettingsController::class => ControllerFactory::class,
            Controller\V0\Admin\BillingSettingsController::class => ControllerFactory::class,
            Controller\V0\IndexController::class => ControllerFactory::class,
            Controller\V0\AuthController::class => ControllerFactory::class,
            Controller\V0\UserController::class => ControllerFactory::class,
            Controller\V0\CompanyController::class => ControllerFactory::class,
            Controller\V0\TeamController::class => ControllerFactory::class,
            Controller\V0\RoleController::class => ControllerFactory::class,
            Controller\V0\CallController::class => ControllerFactory::class,
            Controller\V0\CallSummarizationController::class => ControllerFactory::class,
            Controller\V0\PermissionController::class => ControllerFactory::class,
            Controller\V0\EventController::class => ControllerFactory::class,
            Controller\V0\EventCategoryController::class => ControllerFactory::class,
            Controller\V0\CompanyUsersController::class => ControllerFactory::class,
            Controller\V0\ReportController::class => ControllerFactory::class,
            Controller\V0\ReportTemplateController::class => ControllerFactory::class,
            Controller\V0\UserNotificationController::class => ControllerFactory::class,
            Controller\V0\SearchController::class => ControllerFactory::class,
            Controller\V0\SearchEngineController::class => ControllerFactory::class,
            Controller\V0\CompanyVocabularyController::class => ControllerFactory::class,
            Controller\V0\FrontController::class => ControllerFactory::class,
            Controller\V0\RoboTruckController::class => ControllerFactory::class,
            Controller\V0\DashboardController::class => ControllerFactory::class,
            Controller\V0\EmsController::class => ControllerFactory::class,
            Controller\V0\ChecklistController::class => ControllerFactory::class,
            Controller\V0\AlgoApiController::class => ControllerFactory::class,
            Controller\V0\CompanyLlmEventController::class => ControllerFactory::class,
            Controller\V0\IndustryLlmEventController::class => ControllerFactory::class,
            Controller\V0\CompanyIndustryController::class => ControllerFactory::class,
            Controller\V0\CompanyAlgoApiController::class => ControllerFactory::class,
            Controller\V0\AlgoApiIndustryController::class => ControllerFactory::class,
            Controller\V0\LlmEventController::class => ControllerFactory::class,
            Controller\V0\IndustriesController::class => ControllerFactory::class,
            Controller\V0\IndustryAlgoApiController::class => ControllerFactory::class,
            Controller\V0\Onboarding\OnboardingFormController::class => ControllerFactory::class,
            Controller\V0\Onboarding\OnboardingUsersController::class => ControllerFactory::class,
            Controller\V0\Onboarding\OnboardingIndustriesController::class => ControllerFactory::class,
            Controller\V0\Onboarding\OnboardingCallsSettingsController::class => ControllerFactory::class,
            Controller\RoboMetrics\IndexController::class => ControllerFactory::class,
            Controller\RoboMetrics\DictionaryController::class => ControllerFactory::class,
            Controller\RoboMetrics\CallController::class => ControllerFactory::class,
            Controller\RoboMetrics\ClickhouseController::class => ControllerFactory::class,
            Controller\RoboMetrics\CompanyController::class => ControllerFactory::class,
            Controller\RoboMetrics\AlgoApiController::class => ControllerFactory::class,
            Controller\RoboMetrics\AlgoStatisticsController::class => ControllerFactory::class,
            Controller\RoboMetrics\LanguageDriverController::class => ControllerFactory::class,
            WebhookController::class => ControllerFactory::class,
        ],
    ],
];
