<?php

declare(strict_types=1);

namespace Api;

use Laminas\Router\Http\Method;
use Laminas\Router\Http\Segment;

return [
    'router' => [
        'routes' => [
            'api' => [
                'type' => Segment::class,
                'options' => [
                    'route' => '/api[/]',
                    'defaults' => [
                        'controller' => Controller\V0\IndexController::class,
                        'action' => 'not-found',
                    ],
                ],
                'child_routes' => [
                    'v0' => [
                        'type' => Segment::class,
                        'options' => [
                            'route' => 'v0[/]',
                            'defaults' => [
                                'access-checks' => [
                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                    Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                ],
                            ],
                        ],
                        'child_routes' => [
                            'admin' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'admin[/]',
                                    'defaults' => [
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'company' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'defaults' => [
                                                'controller' => Controller\V0\Admin\CompanyController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                ],
                                            ],
                                            'route' => 'company[/]',
                                        ],
                                        'child_routes' => [
                                            'company' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'update-settings' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'update-settings[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'request' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'PATCH',
                                                                    'defaults' => [
                                                                        'access-checks' => [
                                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                                        ],
                                                                        'action' => 'update-settings',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'billing-settings' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'defaults' => [
                                                                'controller' => Controller\V0\Admin\BillingSettingsController::class,
                                                            ],
                                                            'route' => 'billing-settings[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'get' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'GET',
                                                                    'defaults' => [
                                                                        'access-checks' => [
                                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                                        ],
                                                                        'action' => 'get',
                                                                    ],
                                                                ],
                                                            ],
                                                            'create' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'POST',
                                                                    'defaults' => [
                                                                        'access-checks' => [
                                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                                        ],
                                                                        'action' => 'create',
                                                                    ],
                                                                ],
                                                            ],
                                                            'update' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'PUT',
                                                                    'defaults' => [
                                                                        'access-checks' => [
                                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                                        ],
                                                                        'action' => 'update',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ]
                                            ],
                                            'change-balance' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'change-balance[/]',
                                                ],
                                                'child_routes' => [
                                                    'request' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'change-balance',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'clone-role' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'clone-role[/]',
                                                ],
                                                'child_routes' => [
                                                    'request' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'clone-role',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'general-settings' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'defaults' => [
                                                'controller' => Controller\V0\Admin\GeneralSettingsController::class,
                                            ],
                                            'route' => 'general-settings[/]',
                                        ],
                                        'child_routes' => [
                                            'available-translation-drivers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'available-translation-drivers[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-available-translation-drivers' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-available-translation-drivers',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'available-transcribing-drivers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'available-transcribing-drivers[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-available-transcribing-drivers' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-available-transcribing-drivers',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'translation-drivers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'translation-drivers[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-translation-drivers' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-translation-drivers',
                                                            ],
                                                        ],
                                                    ],
                                                    'set-translation-driver' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'defaults' => [
                                                                'action' => 'set-translation-driver',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'transcribing-drivers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'transcribing-drivers[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-transcribing-drivers' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-transcribing-drivers',
                                                            ],
                                                        ],
                                                    ],
                                                    'set-transcribing-driver' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'defaults' => [
                                                                'action' => 'set-transcribing-driver',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'onboarding' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'defaults' => [
                                                'controller' => Controller\V0\Admin\OnboardingFormController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                ],
                                            ],
                                            'route' => 'onboarding[/]',
                                        ],
                                        'child_routes' => [
                                            'form' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'form[/]',
                                                ],
                                                'child_routes' => [
                                                    'request' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'create-form',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'forms' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'forms[/]',
                                                ],
                                                'child_routes' => [
                                                    'request' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-forms',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'llm-events' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'llm-events[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\LlmEventController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'add-event' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save-event-by-global-admin',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'llm-event' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'llm-event[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\LlmEventController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'llm-event' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'update-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save-event-by-global-admin',
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-event',
                                                            ],
                                                        ],
                                                    ],
                                                    'disconnect' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'disconnect[/]',
                                                            'defaults' => [
                                                                'controller' => Controller\V0\CompanyLlmEventController::class,
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                                ],
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'disconnect-event' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'delete',
                                                                    'defaults' => [
                                                                        'action' => 'disconnect-event-by-global-admin',
                                                                    ],
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
