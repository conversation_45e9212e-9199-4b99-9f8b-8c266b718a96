<?php

declare(strict_types=1);

namespace STCompany\Service;

use Interop\Container\ContainerInterface;
use <PERSON>inas\ServiceManager\Factory\FactoryInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON>all\Data\CallsTable;
use STCompany\Data\ClientsTable;
use STCompany\Data\CompaniesCallTemplatesTable;
use STCompany\Data\CompaniesLanguagesTable;
use STCompany\Data\CompaniesRatesTable;
use STCompany\Data\CompaniesTable;
use STCompany\Data\CompanyDetailsTable;
use STCompany\Data\TeamsTable;
use STCompany\Data\UsersCompaniesLanguagesTable;
use STUser\Data\UsersTable;

class CompanyServiceFactory implements FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return CompanyService
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): CompanyService
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     * @param ContainerInterface $container
     * @param $requestedName
     * @param array|null $options
     * @return CompanyService
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): CompanyService
    {
        $companiesTable = $container->get(CompaniesTable::class);
        $companiesCallTemplatesTable = $container->get(CompaniesCallTemplatesTable::class);
        $companiesRatesTable = $container->get(CompaniesRatesTable::class);
        $usersTable = $container->get(UsersTable::class);
        $companiesLanguagesTable = $container->get(CompaniesLanguagesTable::class);
        $usersCompaniesLanguagesTable = $container->get(UsersCompaniesLanguagesTable::class);
        $teamsTable = $container->get(TeamsTable::class);
        $callsTable = $container->get(CallsTable::class);
        $clientsTable = $container->get(ClientsTable::class);
        $companyDetailsTable = $container->get(CompanyDetailsTable::class);
        $awsConfig = $container->get('config')['aws'];
        return new CompanyService(
            $companiesTable,
            $companiesCallTemplatesTable,
            $companiesRatesTable,
            $usersTable,
            $companiesLanguagesTable,
            $usersCompaniesLanguagesTable,
            $teamsTable,
            $callsTable,
            $clientsTable,
            $companyDetailsTable,
            $awsConfig,
        );
    }
}
