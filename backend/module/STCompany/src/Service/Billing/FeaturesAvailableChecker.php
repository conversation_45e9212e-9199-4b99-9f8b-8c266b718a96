<?php

declare(strict_types=1);

namespace STCompany\Service\Billing;

use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;
use STCompany\Service\Interfaces\BillingRepositoryInterface;

final class FeaturesAvailableChecker
{
    public function __construct(
        private readonly BillingSettingsSelector $billingSettingsSelector,
        private readonly TrialPeriodCalculator $trialPeriodCalculator,
        private readonly BillingRepositoryInterface $billingRepository,
    ) {
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailable(Company $company): bool
    {
        if ($company->getStatus() !== Company::STATUS_TRIAL) {
            return true;
        }

        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $trialDatesData = $this->trialPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        ['start_date' => $startDate, 'end_date' => $endDate] = $trialDatesData;
        $spentCallsSeconds = $this->billingRepository->getCallsSecondsDurationByPeriod(
            $company->getId(),
            $startDate,
            $endDate
        );

        if ($spentCallsSeconds < $billingSettings->getCallsSeconds()) {
            return true;
        }

        return false;
    }

    public function isChatAnalysisAvailable(Company $company): bool
    {
        if ($company->getStatus() !== Company::STATUS_TRIAL) {
            return true;
        }

        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $trialDatesData = $this->trialPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        ['start_date' => $startDate, 'end_date' => $endDate] = $trialDatesData;
        $spentCallsSeconds = $this->billingRepository->getChatsByPeriod(
            $company->getId(),
            $startDate,
            $endDate
        );

        if ($spentCallsSeconds < $billingSettings->getChatsAmount()) {
            return true;
        }

        return false;
    }

    /**
     * @param Call $call
     * @param Company $company
     * @return bool
     */
    public function isCallAnalysisAvailableForCall(Call $call, Company $company): bool
    {
        if ($company->getStatus() !== Company::STATUS_TRIAL) {
            if ($company->getPaidTranscribingTime() < $call->getDuration()) {
                return false;
            }

            return true;
        }

        return true;
    }

    public function isSummarizationAvailable(Company $company): bool
    {
        if (!$company->isSummarizationEnabled()) {
            return false;
        }

        if ($company->getStatus() !== Company::STATUS_TRIAL) {
            return true;
        }

        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $trialDatesData = $this->trialPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        ['start_date' => $startDate, 'end_date' => $endDate] = $trialDatesData;
        $processedSummarizations = $this->billingRepository->getSummarizationsByPeriod(
            $company->getId(),
            $startDate,
            $endDate
        );

        if ($processedSummarizations < $billingSettings->getSummarizationsAmount()) {
            return true;
        }

        return false;
    }

    public function isChecklistAvailable(int $checklistId, Company $company): bool
    {
        if ($company->getStatus() !== Company::STATUS_TRIAL) {
            return true;
        }

        $billingSettings = $this->getBillingSettings($company->getId());
        if (!$billingSettings) {
            return false;
        }

        $trialDatesData = $this->trialPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        ['start_date' => $startDate, 'end_date' => $endDate] = $trialDatesData;
        $processedChecklistCalls = $this->billingRepository->getChecklistCallsByPeriod(
            $company->getId(),
            $checklistId,
            $startDate,
            $endDate
        );

        if ($processedChecklistCalls < $billingSettings->getEachChecklistsCallsAmount()) {
            return true;
        }

        return false;
    }

    private function getBillingSettings(int $companyId): ?BillingSettings
    {
        try {
            return $this->billingSettingsSelector->getBillingSettings($companyId);
        } catch (NotFoundApiException) {
            return null;
        }
    }
}
