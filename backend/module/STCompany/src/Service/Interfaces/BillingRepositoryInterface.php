<?php

declare(strict_types=1);

namespace STCompany\Service\Interfaces;

interface BillingRepositoryInterface
{
    public function getCallsSecondsDurationByPeriod(int $companyId, string $startDate, string $endDate): int;

    public function getChatsByPeriod(int $companyId, string $startDate, string $endDate): int;

    public function getSummarizationsByPeriod(int $companyId, string $startDate, string $endDate): int;

    public function getChecklistCallsByPeriod(
        int $companyId,
        int $checklistId,
        string $startDate,
        string $endDate
    ): int;
}
