<?php

declare(strict_types=1);

namespace STCompany\Validator\BillingSettings;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;

final class UpdateBillingSettingsValidator extends BillingSettingsValidator
{
    private const string ERROR_SETTINGS_DOESNT_EXISTS = 'Billing settings doesn\'t exists.';

    public function __construct(private readonly BillingSettingsSelector $billingSettingsSelector)
    {
    }

    public function run(): void
    {
        $input = $this->getInstance();

        try {
            $this->billingSettingsSelector->getBillingSettings($input['company_id']);
        } catch (NotFoundApiException) {
            $this->addError(
                'company_id',
                self::ERROR_SETTINGS_DOESNT_EXISTS
            );
        }

        parent::run();
    }
}
