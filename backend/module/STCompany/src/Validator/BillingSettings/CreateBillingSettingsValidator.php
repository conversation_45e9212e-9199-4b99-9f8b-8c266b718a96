<?php

declare(strict_types=1);

namespace STCompany\Validator\BillingSettings;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;

final class CreateBillingSettingsValidator extends BillingSettingsValidator
{
    private const string ERROR_SETTINGS_ALREADY_EXISTS = 'Billing settings already exists.';

    public function __construct(private readonly BillingSettingsSelector $billingSettingsSelector)
    {
    }

    public function run(): void
    {
        $input = $this->getInstance();

        try {
            $this->billingSettingsSelector->getBillingSettings($input['company_id']);
            $this->addError(
                'company_id',
                self::ERROR_SETTINGS_ALREADY_EXISTS
            );

            return;
        } catch (NotFoundApiException) {
        }

        parent::run();
    }
}
