<?php

declare(strict_types=1);

namespace STCompany\Entity;

class RoleCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $role
     * @param string|int|null $key
     * @return \STLib\Expand\Collection
     * @throws \RuntimeException
     */
    public function add(mixed $role, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($role instanceof Role)) {
            throw new \RuntimeException('Role must be an instace of "\STCompany\Entity\Role"');
        }
        parent::add($role, $key ?? $role->getId());
        return $this;
    }

    /**
     *
     * @param array|null $attributes
     * @return array
     */
    public function toArray(?array $attributes = null): array
    {
        $result = [];
        foreach ($this as $role) {
            $result[] = $role->toArray($attributes);
        }
        return $result;
    }
}
