<?php

declare(strict_types=1);

namespace STCompany\Controller\Plugin;

use Laminas\Mvc\Controller\Plugin\AbstractPlugin;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCompany\Service\AlgoApi\AlgoApiConnectorService;
use STCompany\Service\AlgoApi\AlgoApisSelectorService;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSaver;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;
use STCompany\Service\Checklist\ChecklistPointService;
use STCompany\Service\Checklist\ChecklistService;
use STCompany\Service\Industry\IndustryConnectorService;
use STCompany\Service\Industry\IndustrySelectorService;
use STCompany\Service\LlmEvent\LlmEventConnectorService;
use STCompany\Service\LlmEvent\LlmEventCreatorService;
use STCompany\Service\LlmEvent\LlmEventSelectorService;
use STCompany\Service\PermissionService;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STCompany\Service\Webhooks\WebhooksSettingsRemover;
use STCompany\Service\Webhooks\WebhooksSettingsSaver;
use STLib\Mvc\Controller\AbstractController;

/**
 * @method AbstractController getController()
 */
class Company extends AbstractPlugin
{
    /**
     * @param string $name
     * @param array $arguments
     * @return mixed
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __call(string $name, array $arguments): mixed
    {
        $companyService = $this->getController()->getServiceManager()->get(\STCompany\Service\CompanyService::class);
        if (!method_exists($companyService, $name)) {
            throw new \BadMethodCallException('Invalid \STCompany\Service\CompanyService method: ' . $name);
        }
        return call_user_func_array([
            $companyService,
            $name
        ], $arguments);
    }

    /**
     *
     * @return \STCompany\Service\RoleService
     */
    public function role(): \STCompany\Service\RoleService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\RoleService::class);
    }

    /**
     *
     * @return \STCompany\Service\UserService
     */
    public function user(): \STCompany\Service\UserService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\UserService::class);
    }

    /**
     *
     * @return \STCompany\Service\TeamService
     */
    public function team(): \STCompany\Service\TeamService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\TeamService::class);
    }

    /**
     *
     * @return \STCompany\Service\ClientService
     */
    public function client(): \STCompany\Service\ClientService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\ClientService::class);
    }

    /**
     *
     * @return \STCompany\Service\ClientPrecalculation\ClientPrecalculationService
     */
    public function clientPrecalculation(): \STCompany\Service\ClientPrecalculation\ClientPrecalculationService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\ClientPrecalculation\ClientPrecalculationService::class);
    }

    /**
     *
     * @return \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService
     */
    public function clientPrecalculationManager(): \STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\ClientPrecalculation\ClientPrecalculationManagerService::class);
    }

    /**
     *
     * @return \STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService
     */
    public function agentPrecalculationManager(): \STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\AgentPrecalculation\AgentPrecalculationManagerService::class);
    }

    /**
     *
     * @return PermissionService
     */
    public function permission(): PermissionService
    {
        return $this->getController()->getServiceManager()->get(PermissionService::class);
    }

    /**
     *
     * @return ChecklistService
     */
    public function checklist(): ChecklistService
    {
        return $this->getController()->getServiceManager()->get(ChecklistService::class);
    }

    /**
     *
     * @return ChecklistPointService
     */
    public function checklistPoint(): ChecklistPointService
    {
        return $this->getController()->getServiceManager()->get(ChecklistPointService::class);
    }

    /**
     *
     * @return \STCompany\Service\EventService
     */
    public function event(): \STCompany\Service\EventService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\EventService::class);
    }

    /**
     *
     * @return \STCompany\Service\EventCategoryService
     */
    public function eventCategory(): \STCompany\Service\EventCategoryService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\EventCategoryService::class);
    }

    /**
     *
     * @return \STCompany\Service\CompanyVocabularyService
     */
    public function vocabulary(): \STCompany\Service\CompanyVocabularyService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\CompanyVocabularyService::class);
    }

    /**
     *
     * @return \STCompany\Service\CompanyAvatarService
     */
    public function avatar(): \STCompany\Service\CompanyAvatarService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\CompanyAvatarService::class);
    }

    /**
     *
     * @return \STCompany\Service\CompanyDataRemoveService
     */
    public function removeData(): \STCompany\Service\CompanyDataRemoveService
    {
        return $this->getController()->getServiceManager()->get(\STCompany\Service\CompanyDataRemoveService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventCreator(): LlmEventCreatorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventCreatorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventSelector(): LlmEventSelectorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventSelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function llmEventConnector(): LlmEventConnectorService
    {
        return $this->getController()->getServiceManager()->get(LlmEventConnectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function industrySelector(): IndustrySelectorService
    {
        return $this->getController()->getServiceManager()->get(IndustrySelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function industryConnector(): IndustryConnectorService
    {
        return $this->getController()->getServiceManager()->get(IndustryConnectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function algoApiConnector(): AlgoApiConnectorService
    {
        return $this->getController()->getServiceManager()->get(AlgoApiConnectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function algoApiSelector(): AlgoApisSelectorService
    {
        return $this->getController()->getServiceManager()->get(AlgoApisSelectorService::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function webhooksSettingsSaver(): WebhooksSettingsSaver
    {
        return $this->getController()->getServiceManager()->get(WebhooksSettingsSaver::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function webhooksSettingsSelector(): WebhookSettingsSelector
    {
        return $this->getController()->getServiceManager()->get(WebhookSettingsSelector::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function webhooksSettingsRemover(): WebhooksSettingsRemover
    {
        return $this->getController()->getServiceManager()->get(WebhooksSettingsRemover::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function billingSettingsSaver(): BillingSettingsSaver
    {
        return $this->getController()->getServiceManager()->get(BillingSettingsSaver::class);
    }

    public function billingSettingsSelector(): BillingSettingsSelector
    {
        return $this->getController()->getServiceManager()->get(BillingSettingsSelector::class);
    }
}
