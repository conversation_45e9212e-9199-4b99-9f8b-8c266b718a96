<?php

declare(strict_types=1);

namespace Console\Command\Queue;

use Symfony\Component\Console\Input\InputOption;
use Console\Command\BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use PhpAmqpLib\Message\AMQPMessage;
use RuntimeException;
use stdClass;

class RepeatQueueProcessingCommand extends BaseCommand
{
    use QueueFilterableTrait;

    /**
     * @var int
     */
    protected const DEFAULT_LIMIT = 200000;
    /**
     *
     * @var string
     */
    protected static $defaultName = 'queue:error:repeat';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Repeat processing of all messages in error queue';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        $this->addOption('error_queue', null, InputOption::VALUE_REQUIRED, 'Error queue name');
        $this->addOption('queue', null, InputOption::VALUE_REQUIRED, 'Queue name');
        $this->addOption('conditions', null, InputOption::VALUE_IS_ARRAY | InputOption::VALUE_OPTIONAL, 'conditions');
        $this->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'limit', static::DEFAULT_LIMIT);
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $queue = $input->getOption('queue');
        $conditions = $input->getOption('conditions');
        $errorQueue = $input->getOption('error_queue');
        $limit = max((int) $input->getOption('limit'), 0);
        if ($limit > self::DEFAULT_LIMIT || $limit <= 0) {
            throw new RuntimeException('Params "limit" cannot be greater than 10,000 and less than 1');
        }

        if (empty($queue) || empty($errorQueue)) {
            throw new RuntimeException('Params "queue" and "error_queue" are required');
        }

        $parsedConditions = $this->parseConditions($conditions);

        $channel = $this->rabbit()->getChannel();
        $messagesToRepublishInErrorQueue = [];
        $suitableMessagesCounter = 0;
        $messagesRead = 0;

        for ($i = 0; $i < $limit; $i++) {
            // basic_get has an error, 2nd argument isn't no_ack but auto_ack
            $data = $channel->basic_get($errorQueue, true);
            if (is_null($data)) {
                $output->writeln(sprintf('Read %d messages from queue. Queue is now empty.', $messagesRead));
                break;
            }
            $messagesRead++;

            $messageBody = json_decode($data->body);

            if (!$messageBody instanceof stdClass) {
                $output->writeln('<error>Invalid JSON in message body, requeueing in error queue.</error>');
                $messagesToRepublishInErrorQueue[] = new AMQPMessage($data->body);
                continue;
            }

            if ($this->isMessageSuitable($messageBody, $parsedConditions)) {
                $messageBody->attempt = 0;
                unset($messageBody->error);
                $message = new AMQPMessage(json_encode($messageBody));
                $channel->basic_publish($message, '', $queue);
                $output->writeln('Moved message: ' . json_encode($messageBody));
                $suitableMessagesCounter++;
            } else {
                $messagesToRepublishInErrorQueue[] = new AMQPMessage(json_encode($messageBody));
            }
        }

        if (!empty($messagesToRepublishInErrorQueue)) {
            foreach ($messagesToRepublishInErrorQueue as $messageToRepublishInErrorQueue) {
                $channel->basic_publish($messageToRepublishInErrorQueue, '', $errorQueue);
            }
            $output->writeln(sprintf('Re-queued %d non-matching or invalid messages back to the error queue.', count($messagesToRepublishInErrorQueue)));
        }

        $output->writeln(sprintf("\nFinished. Moved %d matching messages from '%s' to '%s'.", $suitableMessagesCounter, $errorQueue, $queue));

        return static::SUCCESS;
    }
}
