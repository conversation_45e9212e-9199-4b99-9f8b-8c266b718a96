<?php

declare(strict_types=1);

namespace STApi\ServiceProviding;

use STApi\Entity\Application;
use STApi\Service\ApplicationService;
use STCompany\Entity\Company;
use STCompany\Service\Interfaces\ApplicationCreatorInterface as CompanyApplicationCreatorInterface;

final readonly class ApplicationServiceProvider implements CompanyApplicationCreatorInterface
{
    public function __construct(private ApplicationService $applicationService)
    {
    }

    public function generateApplication(Company $company): Application
    {
        return $this->applicationService->generateApplication($company);
    }
}
