<?php

namespace STAlgo\Validator\Industry;

use STAlgo\Data\AlgoApiIndustriesTable;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Service\Interfaces\IndustrySelectorInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;

class DisconnectIndustryValidator extends Validator
{
    public function __construct(
        private readonly IndustrySelectorInterface $industrySelector,
        private readonly AlgoApisTable $algoApisTable,
        private readonly AlgoApiIndustriesTable $algoApiIndustryTable,
    ) {
    }

    protected const string ERROR_INDUSTRY_DOES_NOT_EXIST = 'The industry does not exists.';
    protected const string ERROR_ALGO_API_DOES_NOT_EXIST = 'The algo api does not exists.';
    protected const string ERROR_INDUSTRY_WAS_NOT_CONNECTED = 'The industry was not connected.';

    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        ['industry_id' => $industryId, 'algo_api_id' => $algoApiId] = $input;

        try {
            $this->industrySelector->getIndustry($industryId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_INDUSTRY_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->algoApisTable->getApis($algoApiId);
        } catch (NotFoundApiException) {
            $this->addError('algo_api_id', self::ERROR_ALGO_API_DOES_NOT_EXIST);

            return;
        }

        try {
            $this->algoApiIndustryTable->getIndustry($industryId, $algoApiId);
        } catch (NotFoundApiException) {
            $this->addError('id', self::ERROR_INDUSTRY_WAS_NOT_CONNECTED);
        }
    }
}
