<?php

namespace STAlgo\Validator;

use STAlgo\Data\AlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Validator\Validator;

class CompareAlgoApisValidator extends Validator
{
    protected const ERROR_ALGO_APIS_WRONG_TYPE = 'Param algo_apis should be an array.';
    protected const ERROR_ALGO_APIS_ALGO_API_ID_MISSED = 'Cannot find algo_api_id param in algo_apis.';
    protected const ERROR_NONEXISTENT_ALGO_APIS = 'Some of algo apis do not found.';

    public function __construct(private readonly AlgoApisTable $algoApisTable)
    {
    }

    /**
     * @throws NotFoundApiException
     */
    public function run(): void
    {
        /** @var array $input */
        $input = $this->getInstance();

        if (!is_array($input)) {
            $this->addError('algo_apis', self::ERROR_ALGO_APIS_WRONG_TYPE);
            return;
        }

        $apiIds = array_column($input, 'algo_api_id');

        if (empty($apiIds)) {
            $this->addError('algo_apis', self::ERROR_ALGO_APIS_ALGO_API_ID_MISSED);
            return;
        }

        $result = $this->algoApisTable->getApis($apiIds);
        if (count($apiIds) !== count($result->toArray())) {
            $this->addError('algo_apis', self::ERROR_NONEXISTENT_ALGO_APIS);
        }
    }
}
