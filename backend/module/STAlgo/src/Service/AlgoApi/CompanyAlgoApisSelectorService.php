<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoApi;

use <PERSON><PERSON>\Db\ResultSet\ResultSet;
use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Entity\AlgoApi;
use <PERSON>A<PERSON>go\Entity\AlgoApiCollection;
use STLib\Mvc\Hydrator\Hydrator;

final class CompanyAlgoApisSelectorService
{
    public function __construct(
        private readonly AlgoApisTable $algoApisTable,
        private readonly Hydrator $hydrator
    ) {
    }

    /**
     * @throws ReflectionException
     */
    public function getNerAlgoApis(int $companyId): AlgoApiCollection
    {
        /**
         * @var ResultSet $algoApisData
         */
        $algoApisData = $this->algoApisTable->getCompanyNerAlgoApis($companyId);

        $algoApiCollection = new AlgoApiCollection();
        foreach ($algoApisData->toArray() as $algoApiData) {
            $algoApi = $this->hydrator->hydrateClass($algoApiData, AlgoApi::class, true);
            $algoApiCollection->add($algoApi);
        }

        return $algoApiCollection;
    }
}
