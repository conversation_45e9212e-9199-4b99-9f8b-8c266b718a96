<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoApi\Update;

use ReflectionException;
use STAlgo\Data\CompaniesAlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApiService;
use STApi\Entity\Exception\NotFoundApiException;
use STIndustry\Data\IndustriesTable;

class AlgoApisAnalyzer
{
    public function __construct(private readonly AlgoApiService $algoApiService)
    {
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function analyze(AlgoApiCollection $providedAlgoApiCollection): AlgoApiCollection
    {
        $existentAlgoApiCollection = $this->algoApiService->getNerAlgoApisIndexedByPath();

        $algoApiCollectionToSave = new AlgoApiCollection();

        foreach ($providedAlgoApiCollection as $providedAlgoApi) {
            if ($this->checkIsNew($providedAlgoApi, $existentAlgoApiCollection)) {
                $this->processNew($providedAlgoApi, $algoApiCollectionToSave);

                continue;
            }

            $existentAlgoApi = $existentAlgoApiCollection->offsetGet($providedAlgoApi->getPath());
            if ($this->isUpdated($providedAlgoApi, $existentAlgoApi)) {
                $this->processUpdated($providedAlgoApi, $existentAlgoApi, $algoApiCollectionToSave);
            }
        }

        foreach ($existentAlgoApiCollection as $existentAlgoApi) {
            if ($this->checkIsDeleted($existentAlgoApi, $providedAlgoApiCollection)) {
                $this->processDeleted($existentAlgoApi, $algoApiCollectionToSave);
            }
        }

        return $algoApiCollectionToSave;
    }

    private function checkIsNew(AlgoApi $providedAlgoApi, AlgoApiCollection $existentAlgoApiCollection): bool
    {
        return !$existentAlgoApiCollection->keyExists($providedAlgoApi->getPath());
    }

    private function processNew(AlgoApi $providedAlgoApi, AlgoApiCollection $algoApiCollectionToSave): void
    {
        $newAlgoApi = new AlgoApi();
        $newAlgoApi->setPath($providedAlgoApi->getPath());
        $newAlgoApi->setIndustryId(IndustriesTable::COMMON_INDUSTRY);
        $newAlgoApi->setAnalyzeMethod(CompaniesAlgoApisTable::NER_ANALYZE_METHOD);
        $newAlgoApi->setName($providedAlgoApi->getName());
        $newAlgoApi->setDescription($providedAlgoApi->getDescription());
        $newAlgoApi->setIsDeleted(false);

        $algoApiCollectionToSave->add($newAlgoApi, $providedAlgoApi->getPath());
    }

    private function isUpdated(AlgoApi $providedAlgoApi, AlgoApi $existentAlgoApi): bool
    {
        return !(
            $providedAlgoApi->getName() === $existentAlgoApi->getName() &&
            $providedAlgoApi->getDescription() === $existentAlgoApi->getDescription()
        );
    }

    private function processUpdated(
        AlgoApi $providedAlgoApi,
        AlgoApi $existentAlgoApi,
        AlgoApiCollection $algoApiCollectionToSave
    ): void {
        $existentAlgoApi->setName($providedAlgoApi->getName());
        $existentAlgoApi->setDescription($providedAlgoApi->getDescription());

        $algoApiCollectionToSave->add($existentAlgoApi, $providedAlgoApi->getPath());
    }

    private function checkIsDeleted(AlgoApi $existentAlgoApi, AlgoApiCollection $providedAlgoApiCollection): bool
    {
        return !$providedAlgoApiCollection->keyExists($existentAlgoApi->getPath());
    }

    private function processDeleted(AlgoApi $existentAlgoApi, AlgoApiCollection $algoApiCollectionToSave): void
    {
        $existentAlgoApi->setIsDeleted(true);

        $algoApiCollectionToSave->add($existentAlgoApi, $existentAlgoApi->getPath());
    }
}
