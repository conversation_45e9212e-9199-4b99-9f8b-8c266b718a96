<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoEvents\RequestCreation;

use STAlgo\Service\ParamsBuilding\RequestParams;
use STCompany\Entity\Company;
use STCompany\Service\LlmEvent\LlmEventSelectorService;
use STConfiguration\Service\ConfigurationService;

class LlmEventsAlgoApiRequestCreator
{
    public function __construct(
        private readonly LlmEventSelectorService $llmEventSelector,
        private readonly ConfigurationService $configuration
    ) {
    }

    public function create(Company $company, RequestParams $requestParams): ?LlmEventsAlgoApiRequest
    {
        $events = $this->llmEventSelector->getLlmEvents($company->getId());

        if ($events->isEmpty()) {
            return null;
        }

        $apiUrl = $this->configuration->get('algo')['algo_api_llm_detection_url'];

        $eventsParams = [];
        foreach ($events as $event) {
            $eventsParams[] = [
                'title' => $event->getName(),
                'description' => $event->getDescription(),
            ];
        }

        $requestParams->addParams('events', $eventsParams);

        return new LlmEventsAlgoApiRequest($apiUrl, $requestParams);
    }
}
