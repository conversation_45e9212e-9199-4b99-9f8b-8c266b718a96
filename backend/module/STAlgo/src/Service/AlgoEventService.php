<?php

declare(strict_types=1);

namespace STAlgo\Service;

use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Data\AlgoEventsHintsTable;
use STAlgo\Data\AlgoEventsTable;
use <PERSON>A<PERSON>go\Entity\AlgoEvent;
use STAlgo\Entity\AlgoEventCollection;
use STLib\Mvc\Hydrator\Hydrator;

class AlgoEventService
{
    use \STLib\Db\ProvidesTransaction;
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var AlgoEventsTable
     */
    protected AlgoEventsTable $algoEventsTable;

    /**
     *
     * @var AlgoEventsHintsTable
     */
    protected AlgoEventsHintsTable $algoEventsHintsTable;

    /**
     *
     * @var Client
     */
    protected Client $algoClient;

    /**
     *
     * @param AlgoEventsTable $algoEventsTable
     * @param AlgoEventsHintsTable $algoEventsHintsTable
     * @param Client $algoClient
     */
    public function __construct(
        AlgoEventsTable $algoEventsTable,
        private AlgoApisTable $algoApisTable,
        AlgoEventsHintsTable $algoEventsHintsTable,
        Client $algoClient,
        private Hydrator $hydrator
    ) {
        $this->algoEventsTable = $algoEventsTable;
        $this->algoEventsHintsTable = $algoEventsHintsTable;
        $this->algoClient = $algoClient;
    }

    /**
     * @param int $companyId
     * @return AlgoEventCollection
     * @throws ReflectionException
     */
    public function getAvailableAlgoEvents(int $companyId): AlgoEventCollection
    {
        $algoEvents = new AlgoEventCollection();
        $algoEventsData = $this->algoEventsTable->getAlgoEvents($companyId);
        foreach ($algoEventsData as $algoEventData) {
            $algoEvents->add($this->hydrate((array) $algoEventData, AlgoEvent::class));
        }
        return $algoEvents;
    }

    /**
     * @throws ReflectionException
     */
    public function getAvailableNerAlgoEvents(int $companyId): AlgoEventCollection
    {
        $algoApisData = $this->algoApisTable->getCompanyNerAlgoApis($companyId);
        $algoApiIds = array_column($algoApisData->toArray(), 'algo_api_id');

        $algoEvents = new AlgoEventCollection();
        $algoEventsData = $this->algoEventsTable->getNerAlgoEvents($algoApiIds);
        foreach ($algoEventsData->toArray() as $algoEventData) {
            $algoEvents->add($this->hydrator->hydrateClass($algoEventData, AlgoEvent::class));
        }
        return $algoEvents;
    }

    /**
     *
     * @param \STAlgo\Entity\AlgoApi $algoApi
     * @return int
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateAlgoEvents(\STAlgo\Entity\AlgoApi $algoApi): int
    {
        try {
            $result = json_decode($this->algoClient->getEvents($algoApi));
        } catch (\Exception $e) {
            return 0;
        }
        $algoEvents = array_filter($result->model_names, function ($event) {
            return $event !== \STCall\Data\CallsAlgoEventsTable::NEUTRAL_EVENT;
        });
        $this->beginTransaction();
        try {
            $this->algoEventsTable->deleteAlgoEvents($algoApi->getId());
            $count = $this->algoEventsTable->insertAlgoEvents($algoApi->getId(), $algoEvents);
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
        return $count;
    }

    /**
     *
     * @param int $algoApiId
     * @param string $algoEvent
     * @param string $algoEventHint
     * @return bool
     */
    public function updateAlgoEventHint(int $algoApiId, string $algoEvent, string $algoEventHint): bool
    {
        return $this->algoEventsHintsTable->updateAlgoEventHint($algoApiId, $algoEvent, $algoEventHint);
    }
}
