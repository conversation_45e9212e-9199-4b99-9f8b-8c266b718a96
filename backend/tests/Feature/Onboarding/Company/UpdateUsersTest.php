<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding\Company;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use tests\Feature\AppTokenTestCase;

final class UpdateUsersTest extends AppTokenTestCase
{
    private const string ERROR_MESSAGE = 'Each user must be an array with name and email fields, both required.';

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateUsers(): void
    {
        $users = [
            [
                'name' => $this->faker->firstName() . ' ' . $this->faker->lastName(),
                'email' => $this->faker->email(),
            ],
            [
                'name' => $this->faker->firstName() . ' ' . $this->faker->lastName(),
                'email' => $this->faker->email(),
            ],
        ];

        $companyName = $this->faker->text(30);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();

        $onboardingFormToSave = $this->createMock(OnboardingForm::class);
        $onboardingFormToSave
            ->expects($this->once())
            ->method('setUsers')
            ->with($users);

        $savedOnboardingForm = new OnboardingForm();
        $savedOnboardingForm->setUsers($users);
        $savedOnboardingForm->setExternalId($externalId);
        $savedOnboardingForm->setFrontFormLink($frontFormLink);
        $savedOnboardingForm->setInviteLink($inviteLink);
        $savedOnboardingForm->setCompanyName($companyName);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturnOnConsecutiveCalls(
                $onboardingFormToSave,
                $onboardingFormToSave,
                $onboardingFormToSave,
                $savedOnboardingForm
            );

        $onboardingFormsTable->expects($this->once())
            ->method('save')
            ->with($onboardingFormToSave);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode(['users' => $users]));

        $this->dispatchApi('onboarding/form/' . $externalId . '/users', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($users, $response['result']['form']['users']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateUsersWhenWrongFormId(): void
    {
        $externalId = $this->faker->uuid();
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/users', 'POST');

        $expectedError = '"id":["The onboarding form does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateUsersWhenDisableForEditForm(): void
    {
        $externalId = $this->faker->uuid();

        $onboardingForm = $this->createMock(OnboardingForm::class);
        $onboardingForm->method('getIsSubmitted')->willReturn(true);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/users', 'POST');

        $expectedError = '"id":["The completed onboarding form is already submitted and disable for edit."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateUsersWhenNoData(): void
    {
        $externalId = $this->faker->uuid();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable->expects($this->never())
            ->method('save');

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId . '/users', 'POST');

        $expectedError = '"users":["' . self::ERROR_MESSAGE . '"]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param mixed $users
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateUsersWhenWrongData(mixed $users): void
    {
        $externalId = $this->faker->uuid();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode(['users' => $users]));

        $this->dispatchApi('onboarding/form/' . $externalId . '/users', 'POST');

        $expectedError = '"users":["' . self::ERROR_MESSAGE . '"]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                'users' => null
            ],
            [
                'users' => '',
            ],
            [
                'users' => [],
            ],
            [
                'users' => [[]],
            ],
            [
                'users' => [
                    [
                        'name' => 'smth',
                        'email' => 'smth',
                    ],
                    [
                        'wrong name field' => 'smth',
                        'email' => 'smth',
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => 'smth',
                        'wrong email field' => 'smth',
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => null,
                        'email' => 'smth',
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => 'smth',
                        'email' => null,
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => str_repeat('a', 256),
                        'email' => 'smth',
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => 'smth',
                        'email' => str_repeat('a', 256),
                    ],
                ],
            ],
            [
                'users' => [
                    [
                        'name' => 'smth',
                        'email' => 'wrong email',
                    ],
                ],
            ],
        ];
    }
}
