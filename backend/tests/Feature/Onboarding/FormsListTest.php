<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCall\Data\CallsTable;
use STCompany\Entity\Role;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Entity\OnboardingFormCollection;
use tests\Feature\AuthTestCase;

final class FormsListTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetForm(): void
    {
        $onboardingForm1 = $this->prepareForm(1, 100);
        $onboardingForm2 = $this->prepareForm(101, 200);

        $onboardingFormCollection = new OnboardingFormCollection();
        $onboardingFormCollection->add($onboardingForm1);
        $onboardingFormCollection->add($onboardingForm2);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getForms')
            ->willReturn($onboardingFormCollection);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/forms', 'GET');

        $expectedData = json_encode([
            'result' => [
                [
                    'id' => $onboardingForm1->getExternalId(),
                    'front_form_link' => $onboardingForm1->getFrontFormLink(),
                    'invite_link' => $onboardingForm1->getInviteLink(),
                    'company_name' => $onboardingForm1->getCompanyName(),
                    'company_logo' => $onboardingForm1->getCompanyLogo(),
                    'users' => $onboardingForm1->getUsers(),
                    'industries' => $onboardingForm1->getIndustries(),
                    'calls_settings' => $onboardingForm1->getCallsSettings(),
                    'is_submitted' => $onboardingForm1->getIsSubmitted(),
                    'company_id' => $onboardingForm1->getCompanyId(),
                ],
                [
                    'id' => $onboardingForm2->getExternalId(),
                    'front_form_link' => $onboardingForm2->getFrontFormLink(),
                    'invite_link' => $onboardingForm2->getInviteLink(),
                    'company_name' => $onboardingForm2->getCompanyName(),
                    'company_logo' => $onboardingForm2->getCompanyLogo(),
                    'users' => $onboardingForm2->getUsers(),
                    'industries' => $onboardingForm2->getIndustries(),
                    'calls_settings' => $onboardingForm2->getCallsSettings(),
                    'is_submitted' => $onboardingForm2->getIsSubmitted(),
                    'company_id' => $onboardingForm2->getCompanyId(),
                ],
            ],
            'error' => null,
        ]);

        $this->assertSame($expectedData, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(200);
    }

    private function prepareForm(int $minId, int $maxId): OnboardingForm
    {
        $languageCodes = array_keys(CallsTable::LANGUAGES);

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setId($this->faker->numberBetween($minId, $maxId));
        $onboardingForm->setCompanyName($this->faker->word());
        $onboardingForm->setExternalId($this->faker->uuid());
        $onboardingForm->setFrontFormLink($this->faker->url());
        $onboardingForm->setInviteLink($this->faker->url());
        $onboardingForm->setCompanyLogo($this->faker->text(1000));
        $onboardingForm->setUsers([
            [
                'name' => $this->faker->firstName() . ' ' . $this->faker->lastName(),
                'email' => $this->faker->email(),
            ]
        ]);
        $industryId1 = $this->faker->numberBetween(101, 200);
        $onboardingForm->setIndustries([
            $industryId1 => [
                'id' => $industryId1,
                'is_active' => false,
                'type' => 'existing',
                'events' => [
                    [
                        'id' => $this->faker->numberBetween(201, 300),
                    ],
                    [
                        'name' => $this->faker->word(),
                        'description' => $this->faker->sentence(),
                    ]
                ]
            ],
        ]);
        $onboardingForm->setCallsSettings([
            'languages' => [
                $this->faker->randomElement($languageCodes),
            ],
            'threshold' => $this->faker->numberBetween(1, 300),
        ]);
        $onboardingForm->setIsSubmitted($this->faker->boolean());
        $onboardingForm->setCompanyId($this->faker->numberBetween(301, 400));

        return $onboardingForm;
    }
}
