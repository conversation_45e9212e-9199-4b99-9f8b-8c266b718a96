<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding\Industries;

use Exception;
use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\EventsAlgoEventsTable;
use STIndustry\Data\IndustriesLlmEventsTable;
use STIndustry\Data\IndustriesTable;
use STIndustry\Entity\Industry;
use STIndustry\Entity\LlmEvent\LlmEvent;
use STIndustry\Entity\LlmEvent\LlmEventCollection;
use STOnboarding\Service\Interfaces\ConfigurationInterface;
use STOnboarding\Service\OnboardingIndustryLlmEventsSelector;
use tests\Feature\AppTokenTestCase;

final class GetIndustryEventsTest extends AppTokenTestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws PHPUnitException
     * @throws ContainerExceptionInterface
     */
    public function testGetEvents(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $industriesTable = $this->createMock(IndustriesTable::class);
        $industriesTable
            ->method('getIndustry')
            ->with($industryId)
            ->willReturn(new Industry());
        $this->serviceManager->setService(IndustriesTable::class, $industriesTable);

        $llmEventId1 = $this->faker->numberBetween(101, 200);
        $llmEventId2 = $this->faker->numberBetween(201, 300);
        $llmEventId3 = $this->faker->numberBetween(301, 400);

        $llmEventName1 = $this->faker->word();
        $llmEventName2 = strtolower($this->faker->text(10));
        $llmEventName3 = strtolower($this->faker->sentence(3));

        $llmEventDesc1 = $this->faker->text(1000);
        $llmEventDesc2 = $this->faker->text(1000);
        $llmEventDesc3 = $this->faker->text(1000);

        $llmEvent1 = new LlmEvent();
        $llmEvent1->setId($llmEventId1);
        $llmEvent1->setName($llmEventName1);
        $llmEvent1->setDescription($llmEventDesc1);
        $llmEvent1->setIndustryId($industryId);

        $llmEvent2 = new LlmEvent();
        $llmEvent2->setId($llmEventId2);
        $llmEvent2->setName($llmEventName2);
        $llmEvent2->setDescription($llmEventDesc2);
        $llmEvent2->setIndustryId($industryId);

        $llmEvent3 = new LlmEvent();
        $llmEvent3->setId($llmEventId3);
        $llmEvent3->setName($llmEventName3);
        $llmEvent3->setDescription($llmEventDesc3);
        $llmEvent3->setIndustryId($industryId);

        $industryLlmEventCollection = new LlmEventCollection([$llmEvent1, $llmEvent2, $llmEvent3]);

        $industriesLlmEventsTable = $this->createMock(IndustriesLlmEventsTable::class);
        $industriesLlmEventsTable
            ->method('getLlmEvents')
            ->with($industryId)
            ->willReturn($industryLlmEventCollection);
        $this->serviceManager->setService(IndustriesLlmEventsTable::class, $industriesLlmEventsTable);

        $eventsAlgoEventsData = [['algo_event' => $llmEventName3], ['algo_event' => $llmEventName1]];

        $eventsAlgoEventsResultSet = new ResultSet();
        $eventsAlgoEventsResultSet->initialize($eventsAlgoEventsData);

        $popularLlmEventsLimit = 2;
        $onboardingConfig = [
            'popular-events-limit' => $popularLlmEventsLimit
        ];

        $configurationMap = [
            ['aws', []],
            ['api', []],
            ['onboarding', $onboardingConfig],
        ];
        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration
            ->method('get')
            ->willReturnMap($configurationMap);
        $this->serviceManager->setService(ConfigurationInterface::class, $configuration);

        $eventsAlgoEventsTable = $this->createMock(EventsAlgoEventsTable::class);
        $eventsAlgoEventsTable
            ->method('orderAlgoEventsByCount')
            ->with([$llmEventName1, $llmEventName2, $llmEventName3], $popularLlmEventsLimit)
            ->willReturn($eventsAlgoEventsResultSet);
        $this->serviceManager->setService(EventsAlgoEventsTable::class, $eventsAlgoEventsTable);

        $this->serviceManager->setService(
            OnboardingIndustryLlmEventsSelector::class,
            $this->serviceManager->build(OnboardingIndustryLlmEventsSelector::class)
        );

        $expectedLlmEvents = [
            [
                'id' => $llmEventId3,
                'name' => $llmEventName3,
                'description' => $llmEventDesc3,
                'industry_id' => $industryId,
            ],
            [
                'id' => $llmEventId1,
                'name' => $llmEventName1,
                'description' => $llmEventDesc1,
                'industry_id' => $industryId,
            ]
        ];

        $this->dispatchApi('onboarding/events?industry_id=' . $industryId, 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($expectedLlmEvents, $response['result']['events']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     */
    public function testGetEventsWhenWrongIndustry(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $industriesTable = $this->createMock(IndustriesTable::class);
        $industriesTable
            ->method('getIndustry')
            ->with($industryId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(IndustriesTable::class, $industriesTable);

        $this->dispatchApi('onboarding/events?industry_id=' . $industryId, 'GET');

        $expectedError = '"industry_id":["The industry does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }
}
