<?php

declare(strict_types=1);

namespace tests\Feature\Admin\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Role;
use STFront\Entity\Front;
use STFront\Service\FrontService;
use ST<PERSON>ib\IdGenerator\IdGeneratorService;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use tests\Feature\AuthTestCase;

final class CreateFormTest extends AuthTestCase
{
    /**
     * @dataProvider companyNameDataProvider
     * @param string|null $companyName
     * @param string|null $expectedCompanyName
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateForm(?string $companyName, ?string $expectedCompanyName): void
    {
        $generatedId = str_replace(' ', '', $this->faker->text(30));

        $idGeneratorService = $this->createMock(IdGeneratorService::class);
        $idGeneratorService
            ->method('generatePseudoUniqueId')
            ->willReturn($generatedId);
        $this->serviceManager->setService(IdGeneratorService::class, $idGeneratorService);

        $domain = $this->faker->domainName();
        $frontFormPath = '/' . $this->faker->word() . '/';
        $invitePath = '/' . $this->faker->word() . '/' . $this->faker->word();

        $front = $this->createMock(Front::class);
        $front->method('getDomain')->willReturn($domain);

        $frontService = $this->createMock(FrontService::class);
        $frontService
            ->method('getActiveFront')
            ->willReturn($front);
        $this->serviceManager->setService(FrontService::class, $frontService);

        $formExternalId = 'ID_' . $generatedId;
        $frontFormLink = 'https://' . $domain . $frontFormPath . $formExternalId;
        $inviteLink = 'https://' . $domain . $invitePath;

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->expects($this->once())
            ->method('save')
            ->with(
                self::callback(
                    function (OnboardingForm $form) use (
                        $expectedCompanyName,
                        $formExternalId,
                        $frontFormLink,
                        $inviteLink
                    ) {
                        return $form->getCompanyName() === $expectedCompanyName
                            && $form->getExternalId() === $formExternalId
                            && $form->getFrontFormLink() === $frontFormLink
                            && $form->getInviteLink() === $inviteLink
                            && is_null($form->getId());
                    }
                ),
            );
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'front_form_path' => $frontFormPath,
            'invite_path' => $invitePath,
            'company_name' => $companyName,
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/form', 'POST');

        $expectedData = json_encode([
            'result' => [
                'form' => [
                    'id' => $formExternalId,
                    'front_form_link' => $frontFormLink,
                    'invite_link' => $inviteLink,
                    'company_name' => $expectedCompanyName,
                    'company_logo' => null,
                    'users' => [],
                    'industries' => [],
                    'calls_settings' => [],
                    'is_submitted' => false,
                    'company_id' => null
                ],
            ],
            'error' => null,
        ]);

        $this->assertSame($expectedData, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(200);
    }

    public static function companyNameDataProvider(): array
    {
        return [
            ['some company name', 'some company name'],
            ['', null],
            [null, null],
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFormWithoutData(): void
    {
        $generatedId = str_replace(' ', '', $this->faker->text(30));

        $idGeneratorService = $this->createMock(IdGeneratorService::class);
        $idGeneratorService
            ->method('generatePseudoUniqueId')
            ->willReturn($generatedId);
        $this->serviceManager->setService(IdGeneratorService::class, $idGeneratorService);

        $domain = $this->faker->domainName();
        $frontFormPath = '/' . $this->faker->word() . '/';
        $invitePath = '/' . $this->faker->word() . '/' . $this->faker->word();

        $front = $this->createMock(Front::class);
        $front->method('getDomain')->willReturn($domain);

        $frontService = $this->createMock(FrontService::class);
        $frontService
            ->method('getActiveFront')
            ->willReturn($front);
        $this->serviceManager->setService(FrontService::class, $frontService);

        $formExternalId = 'ID_' . $generatedId;
        $frontFormLink = 'https://' . $domain . $frontFormPath . $formExternalId;
        $inviteLink = 'https://' . $domain . $invitePath;

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->expects($this->once())
            ->method('save')
            ->with(
                self::callback(
                    function (OnboardingForm $form) use ($formExternalId, $frontFormLink, $inviteLink) {
                        return $form->getCompanyName() === null
                            && $form->getExternalId() === $formExternalId
                            && $form->getFrontFormLink() === $frontFormLink
                            && $form->getInviteLink() === $inviteLink
                            && is_null($form->getId());
                    }
                ),
            );
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'front_form_path' => $frontFormPath,
            'invite_path' => $invitePath
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/form', 'POST');

        $expectedData = json_encode([
            'result' => [
                'form' => [
                    'id' => $formExternalId,
                    'front_form_link' => $frontFormLink,
                    'invite_link' => $inviteLink,
                    'company_name' => null,
                    'company_logo' => null,
                    'users' => [],
                    'industries' => [],
                    'calls_settings' => [],
                    'is_submitted' => false,
                    'company_id' => null
                ],
            ],
            'error' => null,
        ]);

        $this->assertSame($expectedData, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFormWhenTooLongCompanyName(): void
    {
        $companyName = str_repeat('a', 256);

        $frontFormPath = $this->faker->word();
        $invitePath = '/' . $this->faker->word() . '/' . $this->faker->word();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode([
            'front_form_path' => $frontFormPath,
            'invite_path' => $invitePath,
            'company_name' => $companyName
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/form', 'POST');

        $expectedError = '"messages":{"company_name":["Company name must be less than 255 symbols"]}';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFormWhenNoFrontFormPath(): void
    {
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/form', 'POST');

        $expectedError = '"messages":{"front_form_path":["front_form_path is required."]}';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateFormWhenNoInvitePath(): void
    {
        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->expects($this->never())
            ->method('save');
        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->getRequest()->setContent(json_encode(['front_form_path' => $this->faker->word()]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/onboarding/form', 'POST');

        $expectedError = '"messages":{"invite_path":["invite_path is required."]}';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(422);
    }
}
