<?php

declare(strict_types=1);

namespace tests\Feature\Admin\Companies;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Random\RandomException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\Role;
use STCompany\Service\CompanyService;
use tests\Feature\AuthTestCase;

final class UpdateSettingsTest extends AuthTestCase
{
    /**
     * @dataProvider settingNamesDataProvider
     * @param string $settingName
     * @param mixed $value
     * @return void
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdate(string $settingName, mixed $value): void
    {
        $data = [$settingName => $value];

        $this->company->method('toArray')->willReturn($data);

        $companyService = $this->serviceManager->get(CompanyService::class);
        $companyService
            ->expects($this->once())
            ->method('updateSettings')
            ->with($this->companyId, $data);

        $this->getRequest()->setContent(json_encode(['setting_name' => $settingName, 'value' => $value]));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($data, $response['result']['company']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws RandomException
     */
    public static function settingNamesDataProvider(): array
    {
        return [
            ['is_checklists_enabled', (bool) rand(0, 1)],
            ['is_summarization_enabled', (bool) rand(0, 1)],
            ['is_s3_integration_enabled', (bool) rand(0, 1)],
            ['is_manual_import_enabled', (bool) rand(0, 1)],
            ['is_manage_llm_events_by_users_enabled', (bool) rand(0, 1)],
            ['llm_events_limit', random_int(0, 1000)],
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateStatus(): void
    {
        $status = $this->faker->randomElement(Company::$statuses);
        $data = ['status' => $status];

        $this->company->method('toArray')->willReturn($data);

        $companyService = $this->serviceManager->get(CompanyService::class);
        $companyService
            ->expects($this->once())
            ->method('updateSettings')
            ->with($this->companyId, $data);

        $this->getRequest()->setContent(json_encode(['setting_name' => 'status', 'value' => $status]));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($data, $response['result']['company']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateStatusWhenWrongData(): void
    {
        $status = 'some unknown status';
        $data = ['status' => $status];

        $this->company->method('toArray')->willReturn($data);

        $this->getRequest()->setContent(json_encode(['setting_name' => 'status', 'value' => $status]));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'Wrong status, available statuses are: ' . implode(', ', Company::$statuses),
            $response['error']['messages']['value'][0]
        );

        $this->assertResponseStatusCode(422);
    }

    /**
     * @return void
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenWrongCompanyId(): void
    {
        $companyService = $this->serviceManager->get(CompanyService::class);
        $companyService
            ->method('getCompany')
            ->with($this->companyId, $this->globalAdminUser->getId())
            ->willThrowException(new NotFoundApiException('Company not found'));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Company not found', $response['error']['messages']);

        $this->assertResponseStatusCode(404);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenWrongSettingName(): void
    {
        $settingName = 'some nonexistent setting';
        $value = $this->faker->boolean();

        $this->getRequest()->setContent(json_encode(['setting_name' => $settingName, 'value' => $value]));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Wrong setting name.', $response['error']['messages']['setting_name'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider settingNamesDataProvider
     * @param string $settingName
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenWrongSettingValue(string $settingName): void
    {
        $value = 'non boolean or int';

        $this->getRequest()->setContent(json_encode(['setting_name' => $settingName, 'value' => $value]));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Wrong setting value.', $response['error']['messages']['value'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateByNonGlobalAdmin(): void
    {
        $companyService = $this->serviceManager->get(CompanyService::class);
        $companyService
            ->expects($this->never())
            ->method('updateSettings');

        $this->companyAdminUser->method('getFrontIdGlobalAdmin')->willReturn($this->frontId);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/company/' . $this->companyId . '/update-settings', 'PATCH');

        $this->assertResponseStatusCode(401);
    }
}
