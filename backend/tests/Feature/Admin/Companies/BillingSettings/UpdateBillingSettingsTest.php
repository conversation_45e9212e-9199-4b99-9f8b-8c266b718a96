<?php

declare(strict_types=1);

namespace tests\Feature\Admin\Companies\BillingSettings;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\BillingSettingsTable;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class UpdateBillingSettingsTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdate(): void
    {
        $duration = $this->faker->numberBetween(1, 10);
        $callsSeconds = $this->faker->numberBetween(11, 20);
        $chatsAmount = $this->faker->numberBetween(21, 30);
        $summarizationsAmount = $this->faker->numberBetween(31, 40);
        $checklistsAmount = $this->faker->numberBetween(41, 50);
        $eachChecklistsCallsAmount = $this->faker->numberBetween(51, 60);

        $billingSettingsData = [
            'duration' => $duration,
            'calls_seconds' => $callsSeconds,
            'chats_amount' => $chatsAmount,
            'summarizations_amount' => $summarizationsAmount,
            'checklists_amount' => $checklistsAmount,
            'each_checklists_calls_amount' => $eachChecklistsCallsAmount,
        ];

        $billingSettingsRequestData = [
            'duration' => (string) $duration,
            'calls_seconds' => (string) $callsSeconds,
            'chats_amount' => (string) $chatsAmount,
            'summarizations_amount' => (string) $summarizationsAmount,
            'checklists_amount' => (string) $checklistsAmount,
            'each_checklists_calls_amount' => (string) $eachChecklistsCallsAmount,
        ];

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings
            ->method('toArray')
            ->willReturn(array_merge(['company_id' => $this->companyId], $billingSettingsData));

        $billingSettingsTable = $this->createMock(BillingSettingsTable::class);
        $billingSettingsTable
            ->method('getBillingSettings')
            ->with($this->companyId)
            ->willReturn($billingSettings);
        $billingSettingsTable
            ->expects($this->once())
            ->method('updateBillingSettings')
            ->with(
                self::callback(
                    function (BillingSettings $billingSettings) use (
                        $duration,
                        $callsSeconds,
                        $chatsAmount,
                        $summarizationsAmount,
                        $checklistsAmount,
                        $eachChecklistsCallsAmount
                    ) {
                        return $billingSettings->getCompanyId() === $this->companyId
                            && $billingSettings->getDuration() === $duration
                            && $billingSettings->getCallsSeconds() === $callsSeconds
                            && $billingSettings->getChatsAmount() === $chatsAmount
                            && $billingSettings->getSummarizationsAmount() === $summarizationsAmount
                            && $billingSettings->getChecklistsAmount() === $checklistsAmount
                            && $billingSettings->getEachChecklistsCallsAmount() === $eachChecklistsCallsAmount;
                    }
                ),
            );
        $this->serviceManager->setService(BillingSettingsTable::class, $billingSettingsTable);

        $this->getRequest()->setContent(json_encode($billingSettingsRequestData));
        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/company/' . $this->companyId . '/billing-settings', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $billingSettingsData = array_merge(['company_id' => $this->companyId], $billingSettingsData);
        $this->assertSame($billingSettingsData, $response['result']['billing-settings']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenSettingsDoesntExist(): void
    {
        $billingSettingsTable = $this->createMock(BillingSettingsTable::class);
        $billingSettingsTable
            ->method('getBillingSettings')
            ->with($this->companyId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(BillingSettingsTable::class, $billingSettingsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/company/' . $this->companyId . '/billing-settings', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('Billing settings doesn\'t exists.', $response['error']['messages']['company_id'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenWrongData(array $billingSettingsData, string $fieldName): void
    {
        $billingSettingsTable = $this->createMock(BillingSettingsTable::class);
        $billingSettingsTable
            ->method('getBillingSettings')
            ->with($this->companyId)
            ->willReturn($this->createMock(BillingSettings::class));
        $this->serviceManager->setService(BillingSettingsTable::class, $billingSettingsTable);

        $this->getRequest()->setContent(json_encode($billingSettingsData));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/company/' . $this->companyId . '/billing-settings', 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($fieldName . ' must be an integer.', $response['error']['messages'][$fieldName][0]);

        $this->assertResponseStatusCode(422);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                'billingSettingsData' => [
                    'duration' => 'wrong value',
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'duration'
            ],
            [
                'billingSettingsData' => [
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'duration'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 'wrong value',
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'calls_seconds'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'calls_seconds'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 'wrong value',
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'chats_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'chats_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 'wrong value',
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'summarizations_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'summarizations_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 'wrong value',
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'checklists_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'each_checklists_calls_amount' => 10,
                ],
                'fieldName' => 'checklists_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                    'each_checklists_calls_amount' => 'wrong value',
                ],
                'fieldName' => 'each_checklists_calls_amount'
            ],
            [
                'billingSettingsData' => [
                    'duration' => 10,
                    'calls_seconds' => 10,
                    'chats_amount' => 10,
                    'summarizations_amount' => 10,
                    'checklists_amount' => 10,
                ],
                'fieldName' => 'each_checklists_calls_amount'
            ]
        ];
    }
}
