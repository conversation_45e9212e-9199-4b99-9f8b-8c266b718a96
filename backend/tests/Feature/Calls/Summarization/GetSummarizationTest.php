<?php

declare(strict_types=1);

namespace tests\Feature\Calls\Summarization;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCall\Data\CallsTable;
use STCall\Data\CallSummarizationRepository;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class GetSummarizationTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetSummarization(): void
    {
        $callId = $this->faker->uuid();
        $callDate = $this->faker->dateTime()->format('Y-m-d H:i:s');
        $agentId = $this->faker->numberBetween(501, 600);
        $agentName = $this->faker->name();
        $clientName = $this->faker->text(60);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->method('isCallExists')
            ->with($callId, $this->companyId)
            ->willReturn(true);
        $this->serviceManager->setService(CallsTable::class, $callsTable);

        $keyPoints = $this->faker->text(100);
        $customerSentiment = $this->faker->text(100);
        $nextSteps = $this->faker->text(100);
        $primaryPurpose = $this->faker->text(100);
        $mainTopics = $this->faker->text(100);
        $customerProblems = $this->faker->text(100);
        $keyActionItems = $this->faker->text(100);
        $businessOpportunities = $this->faker->text(100);
        $risks = $this->faker->text(100);
        $conversationType = $this->faker->text(100);

        $callSummarizationData = [
            'call_id' => $callId,
            'call_time' => $callDate,
            'company_id' => $this->companyId,
            'key_points' => $keyPoints,
            'customer_sentiment' => $customerSentiment,
            'next_steps' => $nextSteps,
            'primary_purpose' => $primaryPurpose,
            'main_topics' => $mainTopics,
            'customer_problems' => $customerProblems,
            'key_action_items' => $keyActionItems,
            'business_opportunities' => $businessOpportunities,
            'risks' => $risks,
            'conversation_type' => $conversationType,
            'agent_id' => $agentId,
            'agent_name' => $agentName,
            'client_name' => $clientName,
        ];

        $callSummarizationRepository = $this->createMock(CallSummarizationRepository::class);
        $callSummarizationRepository
            ->method('getExtendedSummarizationData')
            ->with($callId, $this->companyId)
            ->willReturn($callSummarizationData);
        $this->serviceManager->setService(CallSummarizationRepository::class, $callSummarizationRepository);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('call/' . $callId . '/summarization', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($callId, $response['result']['summary']['call_id']);
        $this->assertSame($callDate, $response['result']['summary']['call_time']);
        $this->assertSame($this->companyId, $response['result']['summary']['company_id']);
        $this->assertSame($agentId, $response['result']['summary']['agent_id']);
        $this->assertSame($agentName, $response['result']['summary']['agent_name']);
        $this->assertSame($clientName, $response['result']['summary']['client_name']);
        $this->assertSame($keyPoints, $response['result']['summary']['key_points']);
        $this->assertSame($customerSentiment, $response['result']['summary']['customer_sentiment']);
        $this->assertSame($nextSteps, $response['result']['summary']['next_steps']);
        $this->assertSame($primaryPurpose, $response['result']['summary']['primary_purpose']);
        $this->assertSame($mainTopics, $response['result']['summary']['main_topics']);
        $this->assertSame($customerProblems, $response['result']['summary']['customer_problems']);
        $this->assertSame($keyActionItems, $response['result']['summary']['key_action_items']);
        $this->assertSame($businessOpportunities, $response['result']['summary']['business_opportunities']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetSummarizationWhenNoSummarization(): void
    {
        $callId = $this->faker->uuid();

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->method('isCallExists')
            ->with($callId, $this->companyId)
            ->willReturn(true);
        $this->serviceManager->setService(CallsTable::class, $callsTable);

        $callSummarizationRepository = $this->createMock(CallSummarizationRepository::class);
        $callSummarizationRepository
            ->method('getExtendedSummarizationData')
            ->with($callId, $this->companyId)
            ->willReturn([]);
        $this->serviceManager->setService(CallSummarizationRepository::class, $callSummarizationRepository);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('call/' . $callId . '/summarization', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertNull($response['result']['summary']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetSummarizationWhenNoAccessToCall(): void
    {
        $callId = $this->faker->uuid();

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->method('isCallExists')
            ->with($callId, $this->companyId)
            ->willReturn(false);
        $this->serviceManager->setService(CallsTable::class, $callsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('call/' . $callId . '/summarization', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('The call does not exists.', $response['error']['messages']['id'][0]);
        $this->assertResponseStatusCode(422);
    }
}
