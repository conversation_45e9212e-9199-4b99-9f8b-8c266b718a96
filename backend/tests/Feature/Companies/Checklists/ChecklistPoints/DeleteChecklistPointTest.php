<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists\ChecklistPoints;

use Carbon\Carbon;
use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsPointsTable;
use STCompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class DeleteChecklistPointTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDelete(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistPointId = $this->faker->numberBetween(101, 200);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->expects($this->once())
            ->method('deleteChecklistPoint')
            ->with($checklistPointId);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertTrue($response['result']['is_deleted']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteByAdmin(int $adminRoleType): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistPointId = $this->faker->numberBetween(101, 200);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertTrue($response['result']['is_deleted']);
        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenReadPermission(): void
    {
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenNoPermissions(): void
    {
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
