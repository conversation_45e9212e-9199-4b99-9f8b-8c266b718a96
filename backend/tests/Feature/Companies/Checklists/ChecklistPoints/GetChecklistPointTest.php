<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists\ChecklistPoints;

use Carbon\Carbon;
use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsPointsTable;
use STCompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class GetChecklistPointTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGet(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);
        $title = $this->faker->word();
        $description = $this->faker->text(1000);
        $expectedActions = $this->faker->text();
        $goodPerformanceDescription = $this->faker->text();
        $badPerformanceDescription = $this->faker->text();
        $goodPerformanceExample = $this->faker->text();
        $badPerformanceExample = $this->faker->text();
        $order = $this->faker->numberBetween(1, 100);
        $isOptional = $this->faker->boolean();
        $triggerCondition = $this->faker->text();

        $checklistPoint = new ChecklistPoint();
        $checklistPoint->setId($checklistPointId);
        $checklistPoint->setChecklistId($checklistId);
        $checklistPoint->setTitle($title);
        $checklistPoint->setDescription($description);
        $checklistPoint->setExpectedActions($expectedActions);
        $checklistPoint->setGoodPerformanceDescription($goodPerformanceDescription);
        $checklistPoint->setBadPerformanceDescription($badPerformanceDescription);
        $checklistPoint->setGoodPerformanceExample($goodPerformanceExample);
        $checklistPoint->setBadPerformanceExample($badPerformanceExample);
        $checklistPoint->setOrder($order);
        $checklistPoint->setIsOptional($isOptional);
        $checklistPoint->setTriggerCondition($triggerCondition);
        $checklistPoint->setUpdatedAt(Carbon::now());

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->expects($this->once())
            ->method('getChecklistPoint')
            ->with($checklistPointId, $this->companyId)
            ->willReturn($checklistPoint);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($checklistPointId, $response['result']['checklist_point']['id']);
        $this->assertSame($checklistId, $response['result']['checklist_point']['checklist_id']);
        $this->assertSame($title, $response['result']['checklist_point']['title']);
        $this->assertSame($description, $response['result']['checklist_point']['description']);
        $this->assertSame($expectedActions, $response['result']['checklist_point']['expected_actions']);
        $this->assertSame($goodPerformanceDescription, $response['result']['checklist_point']['good_performance_description']);
        $this->assertSame($badPerformanceDescription, $response['result']['checklist_point']['bad_performance_description']);
        $this->assertSame($goodPerformanceExample, $response['result']['checklist_point']['good_performance_example']);
        $this->assertSame($badPerformanceExample, $response['result']['checklist_point']['bad_performance_example']);
        $this->assertSame($order, $response['result']['checklist_point']['order']);
        $this->assertSame($isOptional, $response['result']['checklist_point']['is_optional']);
        $this->assertSame($triggerCondition, $response['result']['checklist_point']['trigger_condition']);
        $this->assertSame($now->format('Y-m-d\TH:i:s.u\Z'), $response['result']['checklist_point']['updated_at']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetByAdmin(int $adminRoleType): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $checklistPoint = $this->createMock(ChecklistPoint::class);
        $checklistPoint->method('getId')->willReturn($checklistPointId);
        $checklistPoint->method('getChecklistId')->willReturn($checklistId);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $checklistsPointsTable = $this->createMock(ChecklistsPointsTable::class);
        $checklistsPointsTable
            ->expects($this->once())
            ->method('getChecklistPoint')
            ->with($checklistPointId, $this->companyId)
            ->willReturn($checklistPoint);
        $this->serviceManager->setService(ChecklistsPointsTable::class, $checklistsPointsTable);

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'GET');

        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetWhenNoPermissions(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistPointId = $this->faker->numberBetween(101, 200);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);

        $this->getRequest()->setContent(json_encode(['checklist_id' => $checklistId]));
        $this->dispatchApi('checklist/checklist-point/' . $checklistPointId, 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
