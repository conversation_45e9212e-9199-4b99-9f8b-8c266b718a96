<?php

declare(strict_types=1);

namespace tests\Feature\Companies\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class GetLlmEventListTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testGetList(): void
    {
        $llmEventId1 = $this->faker->numberBetween(101, 200);
        $llmEventId2 = $this->faker->numberBetween(201, 300);

        $name1 = $this->faker->word();
        $name2 = $this->faker->word();

        $description1 = $this->faker->text();
        $description2 = $this->faker->text();

        $companyLlmEvent1 = $this->createMock(CompanyLlmEvent::class);
        $companyLlmEvent1
            ->method('toArray')
            ->willReturn([
                'id' => $llmEventId1,
                'name' => $name1,
                'description' => $description1,
                'company_id' => $this->companyId
            ]);

        $companyLlmEvent2 = $this->createMock(CompanyLlmEvent::class);
        $companyLlmEvent2
            ->method('toArray')
            ->willReturn([
                'id' => $llmEventId2,
                'name' => $name2,
                'description' => $description2,
                'company_id' => $this->companyId
            ]);

        $companyLlmEventsCollection = new LlmEventCollection([$companyLlmEvent1, $companyLlmEvent2]);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvents')
            ->with($this->companyId)
            ->willReturn($companyLlmEventsCollection);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::READ_PERMISSION
        );
        $this->dispatchApi('company/llm-events', 'GET');
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($llmEventId1, $response['result']['events'][0]['id']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testGetListWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/llm-events', 'GET');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
