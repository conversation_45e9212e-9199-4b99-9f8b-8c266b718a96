<?php

declare(strict_types=1);

namespace tests\Feature\Companies\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\Role;
use STLib\Db\TransactionService;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class CreateLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testCreate(): void
    {
        $newEventId = $this->faker->numberBetween(1, 100);
        $name = 'Event name';
        $description = 'Event description';

        $limit = $this->faker->numberBetween(1, 100);

        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);
        $this->company->method('getLlmEventsLimit')->willReturn($limit);

        $transactionService = $this->createMock(TransactionService::class);
        $transactionService->expects($this->once())->method('beginTransaction');
        $transactionService->expects($this->once())->method('commit');
        $this->serviceManager->setService(TransactionService::class, $transactionService);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($name, $description, $newEventId) {
                        $llmEvent->setId($newEventId);
                        return $llmEvent->getName() === $name &&
                            $llmEvent->getDescription() === $description;
                    }
                ),
            );
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEvent = $this->createMock(CompanyLlmEvent::class);
        $companyLlmEvent
            ->method('toArray')
            ->willReturn([
                'id' => $newEventId,
                'name' => $name,
                'description' => $description,
                'company_id' => $this->companyId
            ]);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEventsCount')
            ->with($this->companyId)
            ->willReturn($limit - 1);
        $companyLlmEventsTable
            ->expects($this->once())
            ->method('saveEvent')
            ->with(
                self::callback(
                    function (CompanyLlmEvent $actualCompanyLlmEvent) use ($newEventId) {
                        return $actualCompanyLlmEvent->getId() === $newEventId
                            && $actualCompanyLlmEvent->getCompanyId() === $this->companyId;
                    }
                ),
            );
        $companyLlmEventsTable
            ->expects($this->exactly(2))
            ->method('getLlmEvent')
            ->with($newEventId, $this->companyId)
            ->willReturnOnConsecutiveCalls($this->throwException(new NotFoundApiException()), $companyLlmEvent);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->getRequest()->setContent(json_encode([
            'name' => $name ,
            'description' => $description
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/llm-events', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($newEventId, $response['result']['event']['id']);
        $this->assertSame($name, $response['result']['event']['name']);
        $this->assertSame($description, $response['result']['event']['description']);
        $this->assertSame($this->companyId, $response['result']['event']['company_id']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenManageDisabled(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(false);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/llm-events', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'LLM event manage is currently disabled for your company. Contact support for assistance.',
            $response['error']['message']
        );
        $this->assertArrayHasKey('Content-Type', $this->getResponse()->getHeaders()->toArray());
        $this->assertSame('application/json', $this->getResponse()->getHeaders()->toArray()['Content-Type']);

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenReachLimit(): void
    {
        $limit = $this->faker->numberBetween(1, 100);

        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);
        $this->company->method('getLlmEventsLimit')->willReturn($limit);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEventsCount')
            ->with($this->companyId)
            ->willReturn($limit);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $supportManagerEmail = '<EMAIL>';

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/llm-events', 'POST');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            sprintf(
                'Your company has reached the limit of %d LLM events. Contact us at %s to extend this limit.',
                $limit,
                $supportManagerEmail
            ),
            $response['error']['messages']['name'][0]
        );

        $this->assertArrayHasKey('Content-Type', $this->getResponse()->getHeaders()->toArray());
        $this->assertSame('application/json', $this->getResponse()->getHeaders()->toArray()['Content-Type']);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenReadPermission(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/llm-events', 'POST');
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);

        $this->dispatchApi('company/llm-events', 'POST');
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenWrongData(mixed $name, mixed $description, string $error, string $fieldName): void
    {
        $limit = $this->faker->numberBetween(1, 100);

        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);
        $this->company->method('getLlmEventsLimit')->willReturn($limit);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEventsCount')
            ->with($this->companyId)
            ->willReturn($limit - 1);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->getRequest()->setContent(json_encode([
            'name' => $name,
            'description' => $description
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/llm-events', 'POST');
        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages'][$fieldName][0]);

        $this->assertResponseStatusCode(422);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                'name' => null,
                'description' => 'description',
                'error' => 'Name must be not empty and less than 255 symbols',
                'fieldName' => 'name'
            ],
            [
                'name' => '',
                'description' => 'description',
                'error' => 'Name must be not empty and less than 255 symbols',
                'fieldName' => 'name'
            ],
            [
                'name' => 'name',
                'description' => null,
                'error' => 'Description must be not empty and less than 1024 symbols',
                'fieldName' => 'description'
            ],
            [
                'name' => 'name',
                'description' => '',
                'error' => 'Description must be not empty and less than 1024 symbols',
                'fieldName' => 'description'
            ]
        ];
    }
}
