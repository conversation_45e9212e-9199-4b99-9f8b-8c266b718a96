<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding\EventsCreation;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\Event\Category;
use STCompany\Entity\Event\Color;
use STCompany\Entity\Role;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\CompanyEventBuilder;
use STCompany\Service\EventService;
use STCompany\Service\RoleService;
use STLlmEvent\Entity\LlmEvent;
use tests\TestCase;

final class CompanyEventBuilderTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testBuildCategory(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $roleId = $this->faker->numberBetween(101, 200);

        $roleService = $this->createMock(RoleService::class);
        $roleService
            ->method('getRoleIdByType')
            ->with(Role::COMPANY_ADMIN_ROLE_TYPE, $companyId)
            ->willReturn($roleId);

        $orangeColorId = $this->faker->numberBetween(201, 300);
        $color = $this->createMock(Color::class);
        $color->method('getId')->willReturn($orangeColorId);

        $eventService = $this->createMock(EventService::class);
        $eventService
            ->method('getColorByName')
            ->with('orange')
            ->willReturn($color);

        $builder = new CompanyEventBuilder($roleService, $eventService);
        $category = $builder->buildCategory($company);

        $this->assertSame('General', $category->getName());
        $this->assertSame($roleId, $category->getRole()->getId());
        $this->assertSame($companyId, $category->getRole()->getCompanyId());
        $this->assertSame($orangeColorId, $category->getColor()->getId());
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testBuildCategoryWhenNoColor(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $roleId = $this->faker->numberBetween(101, 200);

        $roleService = $this->createMock(RoleService::class);
        $roleService
            ->method('getRoleIdByType')
            ->with(Role::COMPANY_ADMIN_ROLE_TYPE, $companyId)
            ->willReturn($roleId);

        $eventService = $this->createMock(EventService::class);
        $eventService
            ->method('getColorByName')
            ->with('orange')
            ->willReturn(null);

        $builder = new CompanyEventBuilder($roleService, $eventService);
        $category = $builder->buildCategory($company);

        $this->assertSame('General', $category->getName());
        $this->assertSame($roleId, $category->getRole()->getId());
        $this->assertNull($category->getColor()->getId());
    }

    /**
     * @throws Exception
     */
    public function testBuildCompanyEvent(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $roleId = $this->faker->numberBetween(101, 200);
        $role = $this->createMock(Role::class);
        $role->method('getId')->willReturn($roleId);

        $categoryId = $this->faker->numberBetween(201, 300);
        $category = $this->createMock(Category::class);
        $category->method('getId')->willReturn($categoryId);
        $category->method('getRole')->willReturn($role);

        $llmEventName = $this->faker->word();
        $llmEvent = $this->createMock(LlmEvent::class);
        $llmEvent->method('getName')->willReturn($llmEventName);

        $roleService = $this->createMock(RoleService::class);
        $eventService = $this->createMock(EventService::class);

        $builder = new CompanyEventBuilder($roleService, $eventService);
        $event = $builder->buildCompanyEvent($llmEvent, $company, $category);

        $this->assertSame($llmEventName, $event->getName());
        $this->assertSame('', $event->getIcon());
        $this->assertSame($roleId, $event->getRole()->getId());
        $this->assertSame($companyId, $event->getRole()->getCompanyId());
        $this->assertSame($categoryId, $event->getCategoryId());
        $this->assertSame($categoryId, $event->getCategoryId());
        $this->assertTrue($event->isPinned());
        $this->assertFalse($event->isConfirmNeeded());
        $this->assertSame(1, $event->getScore());
        $this->assertSame([$llmEventName], $event->getAlgoEvents());
    }
}
