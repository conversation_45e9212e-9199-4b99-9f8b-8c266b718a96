<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\CompanyCreation\Onboarding;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\Company;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\CompanyEventsCreator;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsConnector;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreation\LlmEventsCreator;
use STCompany\Service\CompanyCreation\Onboarding\EventsCreator;
use STLlmEvent\Entity\LlmEventCollection;
use tests\TestCase;

final class EventsCreatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreateEvents(): void
    {
        $company = $this->createMock(Company::class);

        $events = [['event1 data'],['event2 data']];

        $llmEventCollection = $this->createMock(LlmEventCollection::class);

        $llmEventsCreator = $this->createMock(LlmEventsCreator::class);
        $llmEventsCreator
            ->method('createLlmEvents')
            ->with($events)
            ->willReturn($llmEventCollection);

        $llmEventsConnector = $this->createMock(LlmEventsConnector::class);
        $llmEventsConnector
            ->expects($this->once())
            ->method('connectEvents')
            ->with($company, $llmEventCollection);

        $companyEventsCreator = $this->createMock(CompanyEventsCreator::class);
        $companyEventsCreator
            ->expects($this->once())
            ->method('createCompanyEvents')
            ->with($llmEventCollection, $company);

        $creator = new EventsCreator($llmEventsCreator, $llmEventsConnector, $companyEventsCreator);
        $creator->createEvents($company, $events);
    }
}
