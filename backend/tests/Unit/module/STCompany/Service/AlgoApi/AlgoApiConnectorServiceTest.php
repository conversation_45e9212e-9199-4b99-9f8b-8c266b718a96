<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\AlgoApi;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Data\CompaniesAlgoApisTable;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\AlgoApi\AlgoApi;
use STCompany\Service\AlgoApi\AlgoApiConnectorService;
use STCompany\Service\AlgoApi\AlgoApisSelectorService;
use tests\TestCase;

final class AlgoApiConnectorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testConnect(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $algoApiId = $this->faker->numberBetween(101, 200);

        $companyAlgoApi = $this->createMock(AlgoApi::class);
        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);
        $companiesAlgoApisTable
            ->expects($this->once())
            ->method('saveAlgoApi')
            ->with(
                self::callback(
                    function (AlgoApi $companyAlgoApi) use ($companyId, $algoApiId) {
                        return $companyAlgoApi->getCompanyId() === $companyId
                            && $companyAlgoApi->getId() === $algoApiId;
                    }
                ),
            );

        $companiesAlgoApisSelector = $this->createMock(AlgoApisSelectorService::class);
        $companiesAlgoApisSelector
            ->method('getAlgoApi')
            ->with($algoApiId, $companyId)
            ->willReturn($companyAlgoApi);

        $connector = new AlgoApiConnectorService($companiesAlgoApisTable, $companiesAlgoApisSelector);

        $this->assertSame($companyAlgoApi, $connector->connect($algoApiId, $companyId));
    }

    /**
     * @throws Exception
     */
    public function testDisconnect(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $algoApiId = $this->faker->numberBetween(101, 200);

        $companiesAlgoApisTable = $this->createMock(CompaniesAlgoApisTable::class);
        $companiesAlgoApisTable
            ->expects($this->once())
            ->method('deleteAlgoApi')
            ->with($algoApiId, $companyId);

        $companiesAlgoApisSelector = $this->createMock(AlgoApisSelectorService::class);

        $connector = new AlgoApiConnectorService($companiesAlgoApisTable, $companiesAlgoApisSelector);
        $connector->disconnect($algoApiId, $companyId);
    }
}
