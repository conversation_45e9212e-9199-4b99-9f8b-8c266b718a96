<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\Billing;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STCompany\Service\Billing\TrialPeriodCalculator;
use tests\TestCase;

final class TrialPeriodCalculatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCalculateTrialPeriod(): void
    {
        $createdAtAgoDays = 5;
        $createdAt = (Carbon::now())->subDays($createdAtAgoDays);

        $trialDuration = 20;

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings->method('getDuration')->willReturn(20);

        $company = $this->createMock(Company::class);
        $company->method('getCreatedAt')->willReturn($createdAt);

        $trialPeriodCalculator = new TrialPeriodCalculator();
        $trialDates = $trialPeriodCalculator->calculateTrialPeriod($company, $billingSettings);

        $this->assertArrayHasKey('start_date', $trialDates);
        $this->assertArrayHasKey('end_date', $trialDates);

        $this->assertSame($createdAt->format('Y-m-d'), $trialDates['start_date']);
        $this->assertSame($createdAt->addDays($trialDuration)->format('Y-m-d'), $trialDates['end_date']);
    }
}
