<?php

declare(strict_types=1);

namespace tests\Unit\module\STCompany\Service\Billing;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCompany\Entity\BillingSettings\BillingSettings;
use STCompany\Entity\Company;
use STCompany\Service\Billing\BillingSettings\BillingSettingsSelector;
use STCompany\Service\Billing\FeaturesAvailableChecker;
use STCompany\Service\Billing\TrialPeriodCalculator;
use STCompany\Service\Interfaces\BillingRepositoryInterface;
use tests\TestCase;

final class FeaturesAvailableCheckerTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableWhenActiveStatus(): void
    {
        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_ACTIVE);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertTrue($featuresAvailableChecker->isCallAnalysisAvailable($company));
    }

    /**
     * @dataProvider availableValuesProvider
     * @param int $spentValue
     * @param int $availableValue
     * @param bool $result
     * @return void
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableWhenTrialStatus(
        int $spentValue,
        int $availableValue,
        bool $result
    ): void {
        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);
        $company->method('getId')->willReturn($companyId);

        $startTrailDate = $this->faker->date();
        $endTrailDate = $this->faker->date();

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings->method('getCallsSeconds')->willReturn($availableValue);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willReturn($billingSettings);

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $trialPeriodCalculator
            ->method('calculateTrialPeriod')
            ->with($company, $billingSettings)
            ->willReturn(['start_date' => $startTrailDate, 'end_date' => $endTrailDate]);

        $billingRepository = $this->createMock(BillingRepositoryInterface::class);
        $billingRepository
            ->method('getCallsSecondsDurationByPeriod')
            ->with($companyId, $startTrailDate, $endTrailDate)
            ->willReturn($spentValue);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );
        $this->assertSame($result, $featuresAvailableChecker->isCallAnalysisAvailable($company));
    }

    public static function availableValuesProvider(): array
    {
        return [
            [
                'spentValue' => 99,
                'availableValue' => 100,
                'result' => true
            ],
            [
                'spentValue' => 100,
                'availableValue' => 100,
                'result' => false
            ],
            [
                'spentValue' => 101,
                'availableValue' => 100,
                'result' => false
            ]
        ];
    }

    /**
     * @return void
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableWhenTrialAndNoBillingSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);
        $company->method('getId')->willReturn($companyId);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willThrowException(new NotFoundApiException());

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );
        $this->assertFalse($featuresAvailableChecker->isCallAnalysisAvailable($company));
    }

    /**
     * @throws Exception
     */
    public function testIsChatAnalysisAvailableWhenActiveStatus(): void
    {
        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_ACTIVE);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertTrue($featuresAvailableChecker->isChatAnalysisAvailable($company));
    }

    /**
     * @dataProvider availableValuesProvider
     * @param int $spentValue
     * @param int $availableValue
     * @param bool $result
     * @return void
     * @throws Exception
     */
    public function testIsChatAnalysisAvailableWhenTrialStatus(
        int $spentValue,
        int $availableValue,
        bool $result
    ): void {
        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);
        $company->method('getId')->willReturn($companyId);

        $startTrailDate = $this->faker->date();
        $endTrailDate = $this->faker->date();

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings->method('getChatsAmount')->willReturn($availableValue);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willReturn($billingSettings);

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $trialPeriodCalculator
            ->method('calculateTrialPeriod')
            ->with($company, $billingSettings)
            ->willReturn(['start_date' => $startTrailDate, 'end_date' => $endTrailDate]);

        $billingRepository = $this->createMock(BillingRepositoryInterface::class);
        $billingRepository
            ->method('getChatsByPeriod')
            ->with($companyId, $startTrailDate, $endTrailDate)
            ->willReturn($spentValue);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );
        $this->assertSame($result, $featuresAvailableChecker->isChatAnalysisAvailable($company));
    }

    /**
     * @throws Exception
     */
    public function testIsChatAnalysisAvailableWhenTrialAndNoBillingSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);
        $company->method('getId')->willReturn($companyId);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willThrowException(new NotFoundApiException());

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );
        $this->assertFalse($featuresAvailableChecker->isChatAnalysisAvailable($company));
    }

    /**
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableForCallWhenActiveStatusAndEnoughPaidTime(): void
    {
        $paidTranscribingTime = $this->faker->numberBetween(51, 100);
        $callDuration = $this->faker->numberBetween(1, 50);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_ACTIVE);
        $company->method('getPaidTranscribingTime')->willReturn((float) $paidTranscribingTime);

        $call = $this->createMock(Call::class);
        $call->method('getDuration')->willReturn($callDuration);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertTrue($featuresAvailableChecker->isCallAnalysisAvailableForCall($call, $company));
    }

    /**
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableForCallWhenActiveStatusAndNotEnoughPaidTime(): void
    {
        $paidTranscribingTime = $this->faker->numberBetween(51, 100);
        $callDuration = $this->faker->numberBetween(101, 200);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_ACTIVE);
        $company->method('getPaidTranscribingTime')->willReturn((float) $paidTranscribingTime);

        $call = $this->createMock(Call::class);
        $call->method('getDuration')->willReturn($callDuration);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertFalse($featuresAvailableChecker->isCallAnalysisAvailableForCall($call, $company));
    }

    /**
     * @return void
     * @throws Exception
     */
    public function testIsCallAnalysisAvailableForCallWhenTrialStatus(): void
    {
        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);
        $call = $this->createMock(Call::class);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertTrue($featuresAvailableChecker->isCallAnalysisAvailableForCall($call, $company));
    }

    /**
     * @throws Exception
     */
    public function testIsSummarizationAvailableWhenActiveStatus(): void
    {
        $isEnabled = $this->faker->boolean;
        $company = $this->createMock(Company::class);
        $company->method('isSummarizationEnabled')->willReturn($isEnabled);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertSame($isEnabled, $featuresAvailableChecker->isSummarizationAvailable($company));
    }

    /**
     * @return void
     * @throws Exception
     */
    public function testIsSummarizationAvailableWhenTrialStatusAndNotEnabled(): void
    {
        $company = $this->createMock(Company::class);
        $company->method('isSummarizationEnabled')->willReturn(false);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertFalse($featuresAvailableChecker->isSummarizationAvailable($company));
    }

    /**
     * @dataProvider availableValuesProvider
     * @param int $spentValue
     * @param int $availableValue
     * @param bool $result
     * @return void
     * @throws Exception
     */
    public function testIsSummarizationAvailableWhenTrialStatusAndEnabled(
        int $spentValue,
        int $availableValue,
        bool $result
    ): void {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('isSummarizationEnabled')->willReturn(true);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings->method('getSummarizationsAmount')->willReturn($availableValue);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willReturn($billingSettings);

        $startTrailDate = $this->faker->date();
        $endTrailDate = $this->faker->date();

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $trialPeriodCalculator
            ->method('calculateTrialPeriod')
            ->with($company, $billingSettings)
            ->willReturn(['start_date' => $startTrailDate, 'end_date' => $endTrailDate]);

        $billingRepository = $this->createMock(BillingRepositoryInterface::class);
        $billingRepository
            ->method('getSummarizationsByPeriod')
            ->with($companyId, $startTrailDate, $endTrailDate)
            ->willReturn($spentValue);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertSame($result, $featuresAvailableChecker->isSummarizationAvailable($company));
    }

    /**
     * @throws Exception
     */
    public function testIsSummarizationAvailableWhenTrialStatusAndNoBillingSettings(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willThrowException(new NotFoundApiException());

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertFalse($featuresAvailableChecker->isSummarizationAvailable($company));
    }

    /**
     * @throws Exception
     */
    public function testIsChecklistAvailableWhenActiveStatus(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getStatus')->willReturn(Company::STATUS_ACTIVE);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $billingRepository = $this->createMock(BillingRepositoryInterface::class);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertTrue($featuresAvailableChecker->isChecklistAvailable($checklistId, $company));
    }

    /**
     * @dataProvider availableValuesProvider
     * @param int $spentValue
     * @param int $availableValue
     * @param bool $result
     * @return void
     * @throws Exception
     */
    public function testIsChecklistAvailableWhenTrialStatus(
        int $spentValue,
        int $availableValue,
        bool $result
    ): void {
        $checklistId = $this->faker->numberBetween(1, 100);

        $companyId = $this->faker->numberBetween(101, 200);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getStatus')->willReturn(Company::STATUS_TRIAL);

        $billingSettings = $this->createMock(BillingSettings::class);
        $billingSettings->method('getEachChecklistsCallsAmount')->willReturn($availableValue);

        $billingSettingsSelector = $this->createMock(BillingSettingsSelector::class);
        $billingSettingsSelector
            ->method('getBillingSettings')
            ->with($companyId)
            ->willReturn($billingSettings);

        $startTrailDate = $this->faker->date();
        $endTrailDate = $this->faker->date();

        $trialPeriodCalculator = $this->createMock(TrialPeriodCalculator::class);
        $trialPeriodCalculator
            ->method('calculateTrialPeriod')
            ->with($company, $billingSettings)
            ->willReturn(['start_date' => $startTrailDate, 'end_date' => $endTrailDate]);

        $billingRepository = $this->createMock(BillingRepositoryInterface::class);
        $billingRepository
            ->method('getChecklistCallsByPeriod')
            ->with($companyId, $checklistId, $startTrailDate, $endTrailDate)
            ->willReturn($spentValue);

        $featuresAvailableChecker = new FeaturesAvailableChecker(
            $billingSettingsSelector,
            $trialPeriodCalculator,
            $billingRepository
        );

        $this->assertSame($result, $featuresAvailableChecker->isChecklistAvailable($checklistId, $company));
    }
}
