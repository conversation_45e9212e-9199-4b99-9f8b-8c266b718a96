<?php

namespace tests\Unit\module\STLlmEvent\Service;

use PHPUnit\Framework\MockObject\Exception;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use STLlmEvent\Service\LlmEventSaverService;
use tests\TestCase;

class LlmEventSaverServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testSaveNewRecord(): void
    {
        $name = $this->faker->word();
        $description = $this->faker->text(100);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($name, $description): bool {
                        return $name === $llmEvent->getName() && $description === $llmEvent->getDescription();
                    }
                )
            );

        $saver = new LlmEventSaverService($llmEventsTable);

        $this->assertInstanceOf(LlmEvent::class, $saver->save($name, $description));
    }

    /**
     * @throws Exception
     */
    public function testSaveExistentRecord(): void
    {
        $id = $this->faker->numberBetween(1, 100);
        $name = $this->faker->word();
        $description = $this->faker->text(100);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($id, $name, $description): bool {
                        return $id === $llmEvent->getId()
                            && $name === $llmEvent->getName()
                            && $description === $llmEvent->getDescription();
                    }
                )
            );

        $saver = new LlmEventSaverService($llmEventsTable);

        $this->assertInstanceOf(LlmEvent::class, $saver->save($name, $description, $id));
    }
}
