<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\Export;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STCall\Service\Export\FileNameGenerator;
use STCall\Service\Interfaces\ConfigurationInterface;
use STCompany\Entity\Company;
use tests\TestCase;

final class FileNameGeneratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGenerateWhenAllAwsFieldsFilled(): void
    {
        $exportType = $this->faker->word();

        $companyId = $this->faker->numberBetween(1, 100);
        $exportBucketName = $this->faker->word();
        $region = $this->faker->sentence(3);
        $directory = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketDir')->willReturn($directory);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $env = 'test';

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration->method('get')->with('api')->willReturn(['env' => $env]);

        $fileName = sprintf(
                '%s/%s/%s/%s-%s',
                $env,
                $directory,
                $exportType,
                $startDate->format('Y_m_d-H:i:s'),
                $endDate->format('Y_m_d-H:i:s')
            ) . '.csv';

        $generator = new FileNameGenerator($configuration);
        $this->assertSame($fileName, $generator->generate($company, $exportType, $startDate, $endDate));
    }

    /**
     * @dataProvider emptyDirDataProvider
     * @param string|null $dir
     * @return void
     * @throws Exception
     */
    public function testGenerateWhenNoDirectory(?string $dir): void
    {
        $exportType = $this->faker->word();

        $companyId = $this->faker->numberBetween(1, 100);
        $exportBucketName = $this->faker->word();
        $region = $this->faker->sentence(3);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketDir')->willReturn($dir);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $env = 'test';

        $configuration = $this->createMock(ConfigurationInterface::class);
        $configuration->method('get')->with('api')->willReturn(['env' => $env]);

        $fileName = sprintf(
                '%s/%s/%s-%s',
                $env,
                $exportType,
                $startDate->format('Y_m_d-H:i:s'),
                $endDate->format('Y_m_d-H:i:s')
            ) . '.csv';

        $generator = new FileNameGenerator($configuration);
        $this->assertSame($fileName, $generator->generate($company, $exportType, $startDate, $endDate));
    }

    public static function emptyDirDataProvider(): array
    {
        return [
            [null],
            ['']
        ];
    }

    /**
     * @dataProvider awsDataProvider
     * @param string|null $exportBucketName
     * @param string|null $region
     * @return void
     * @throws Exception
     */
    public function testGenerateWhenRequiredDate(?string $exportBucketName, ?string $region): void
    {
        $exportType = $this->faker->word();

        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketDir')->willReturn(null);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $configuration = $this->createMock(ConfigurationInterface::class);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage(
            sprintf(
                'Fields %s or %s are not filled in for the company. Company ID: %d',
                'aws_s3_export_bucket_name',
                'aws_s3_export_bucket_region',
                $companyId
            )
        );

        $generator = new FileNameGenerator($configuration);
        $generator->generate($company, $exportType, $startDate, $endDate);
    }

    public static function awsDataProvider(): array
    {
        return [
            [null, null],
            [null, ''],
            ['', null],
            ['', '']
        ];
    }
}
