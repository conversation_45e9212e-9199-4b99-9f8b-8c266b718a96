<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\Webhooks;

use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Daemon\Webhooks\WebhooksDaemon;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCall\Service\Webhooks\WebhookSender;
use STCall\Service\Webhooks\WebhookServiceFactory;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;
use Throwable;

final class WebhooksDaemonTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ThirdPartyApiException
     * @throws Throwable
     */
    public function testHandle(): void
    {
        $source = $this->faker->sentence();
        $webhookUrl = $this->faker->url();
        $webhookHeaders = [$this->faker->word() => $this->faker->word()];
        $webhookType = $this->faker->word();

        $webhookSettingsData = [
            'url' => $webhookUrl,
            'headers' => $webhookHeaders,
        ];

        $companyId = $this->faker->numberBetween(1, 100);

        $callId = $this->faker->uuid();

        $data = ['original data'];
        $filteredData = ['filtered data'];

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('filterData')->with($data)->willReturn($filteredData);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($source)->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('getCompaniesWebhooksSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn($webhookSettingsData);

        $sendData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $filteredData
        ];
        $webhookSender = $this->createMock(WebhookSender::class);
        $webhookSender->expects($this->once())->method('send')->with($webhookUrl, $sendData, $webhookHeaders);

        $dataCollector = $this->createMock(DataCollector::class);

        $body = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $source,
            'data' => $data,
        ];
        $message = json_encode($body);

        $daemon = new WebhooksDaemon($webhookServiceFactory, $webhookSettingsSelector, $webhookSender, $dataCollector);
        $daemon->handle($message);
    }

    /**
     * @throws Throwable
     * @throws Exception
     * @throws ThirdPartyApiException
     */
    public function testHandleWhenNoHeaders(): void
    {
        $source = $this->faker->sentence();
        $webhookUrl = $this->faker->url();
        $webhookType = $this->faker->word();

        $webhookSettingsData = ['url' => $webhookUrl];

        $companyId = $this->faker->numberBetween(1, 100);

        $callId = $this->faker->uuid();

        $data = ['original data'];
        $filteredData = ['filtered data'];

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('filterData')->with($data)->willReturn($filteredData);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($source)->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('getCompaniesWebhooksSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn($webhookSettingsData);

        $sendData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $filteredData
        ];
        $webhookSender = $this->createMock(WebhookSender::class);
        $webhookSender->expects($this->once())->method('send')->with($webhookUrl, $sendData, null);

        $dataCollector = $this->createMock(DataCollector::class);

        $body = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $source,
            'data' => $data,
        ];
        $message = json_encode($body);

        $daemon = new WebhooksDaemon($webhookServiceFactory, $webhookSettingsSelector, $webhookSender, $dataCollector);
        $daemon->handle($message);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param array $body
     * @return void
     * @throws Exception
     * @throws ThirdPartyApiException
     * @throws Throwable
     */
    public function testHandleWhenWrongData(array $body): void
    {
        $roboTruckEventName = 'send_webhook_fail';

        $message = json_encode($body);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSender = $this->createMock(WebhookSender::class);

        $roboTruckMessage = 'Fail to extract message data.';
        $roboTruckExtra = ['message' => $message];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckMessage, $roboTruckExtra, null);

        $daemon = new WebhooksDaemon($webhookServiceFactory, $webhookSettingsSelector, $webhookSender, $dataCollector);
        $daemon->handle($message);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                [
                    'source' => 'source',
                    'call_id' => 'call_id',
                    'data' => [],
                ],
            ],
            [
                [
                    'company_id' => 123,
                    'call_id' => 'call_id',
                    'data' => [],
                ],
            ],
            [
                [
                    'company_id' => 123,
                    'call_id' => 'call_id',
                    'source' => 'source',
                ],
            ],
            [
                [
                    'company_id' => 123,
                    'source' => 'source',
                    'data' => [],
                ],
            ],
            [
                [
                    'company_id' => 123,
                    'call_id' => 'call_id',
                    'source' => 'source',
                    'data' => 'not array',
                ]
            ]
        ];
    }

    /**
     * @return void
     * @throws Exception
     * @throws ThirdPartyApiException
     * @throws Throwable
     */
    public function testHandleWhenGetWebhookSettingsError(): void
    {
        $roboTruckEventName = 'send_webhook_fail';

        $source = $this->faker->sentence();
        $webhookType = $this->faker->word();

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $data = ['original data'];
        $body = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $source,
            'data' => $data,
        ];
        $message = json_encode($body);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($source)->willReturn($webhookService);

        $error = 'Get settings error';
        $code = $this->faker->numberBetween(0, 1000);
        $exception = new RuntimeException($error, $code);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('getCompaniesWebhooksSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willThrowException($exception);

        $webhookSender = $this->createMock(WebhookSender::class);

        $roboTruckMessage = 'Fail to get webhook settings.';
        $roboTruckExtra = [
            'source' => $source,
            'webhook_type' => $webhookType,
            'error_message' => $error,
            'error_code' => $code,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckMessage, $roboTruckExtra, null);

        $daemon = new WebhooksDaemon($webhookServiceFactory, $webhookSettingsSelector, $webhookSender, $dataCollector);
        $daemon->handle($message);
    }

    /**
     * @throws Throwable
     * @throws Exception
     * @throws ThirdPartyApiException
     */
    public function testHandleWhenSendError(): void
    {
        $roboTruckEventName = 'send_webhook_fail';

        $source = $this->faker->sentence();
        $webhookUrl = $this->faker->url();
        $webhookType = $this->faker->word();

        $webhookSettingsData = ['url' => $webhookUrl];

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $data = ['original data'];
        $filteredData = ['filtered data'];
        $body = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'source' => $source,
            'data' => $data,
        ];
        $message = json_encode($body);

        $webhookService = $this->createMock(WebhookServiceInterface::class);
        $webhookService->method('filterData')->with($data)->willReturn($filteredData);
        $webhookService->method('getType')->willReturn($webhookType);

        $webhookServiceFactory = $this->createMock(WebhookServiceFactory::class);
        $webhookServiceFactory->method('create')->with($source)->willReturn($webhookService);

        $webhookSettingsSelector = $this->createMock(WebhookSettingsSelector::class);
        $webhookSettingsSelector
            ->method('getCompaniesWebhooksSettingsDataByType')
            ->with($webhookType, $companyId)
            ->willReturn($webhookSettingsData);

        $sendData = [
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $filteredData
        ];
        $error = 'some error';
        $code = $this->faker->numberBetween(0, 1000);
        $exception = new RuntimeException($error, $code);
        $webhookSender = $this->createMock(WebhookSender::class);
        $webhookSender
            ->method('send')
            ->with($webhookUrl, $sendData, null)
            ->willThrowException($exception);

        $roboTruckMessage = 'Fail to send data to company.';
        $roboTruckExtra = [
            'source' => $source,
            'webhook_type' => $webhookType,
            'webhook_url' => $webhookUrl,
            'headers' => null,
            'data' => $sendData,
            'error_message' => $error,
            'error_code' => $code,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckMessage, $roboTruckExtra, null);

        $daemon = new WebhooksDaemon($webhookServiceFactory, $webhookSettingsSelector, $webhookSender, $dataCollector);
        $daemon->handle($message);
    }
}
