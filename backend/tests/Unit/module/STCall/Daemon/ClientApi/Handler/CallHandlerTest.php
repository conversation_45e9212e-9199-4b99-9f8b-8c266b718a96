<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\ClientApi\Handler;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCall\Daemon\Analysis\FileDownloadStepDaemon;
use STCall\Daemon\ClientApi\Handler\CallHandler;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\UploadParams\UploadParams;
use STCall\Service\Import\UploadService;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class CallHandlerTest extends TestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testRunWhenRecordingFile(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $requestId = $this->faker->uuid;

        $requestBody = ['recording_file' => $this->faker->text()];

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isCallAnalysisAvailable')
            ->with($company)
            ->willReturn(true);

        $dataCollector = $this->createMock(DataCollector::class);

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(
                $companyId,
                null,
                FileDownloadStep::CALL_FILE_DOWNLOAD_QUEUE,
                FileDownloadStepDaemon::RABBITMQ_EXCHANGE_NAME,
                [
                    'params' => $requestBody,
                    'request_id' => $requestId,
                ],
                $this->anything()
            );

        $uploadService = $this->createMock(UploadService::class);
        $uploadService->expects($this->never())->method('uploadCall');

        $handler = new CallHandler(
            $featuresAvailableChecker,
            $dataCollector,
            $uploadService,
            $callAnalysisService,
            $company,
            $requestId
        );

        $this->assertTrue($handler->run($requestBody));
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testRunWhenNoRecordingFile(): void
    {
        $company = $this->createMock(Company::class);

        $requestId = $this->faker->uuid;
        $requestBody = ['some_key' => 'some value'];

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isCallAnalysisAvailable')
            ->with($company)
            ->willReturn(true);

        $dataCollector = $this->createMock(DataCollector::class);

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService->expects($this->never())->method('addToQueue');

        $uploadService = $this->createMock(UploadService::class);
        $uploadService
            ->expects($this->once())
            ->method('uploadCall')
            ->with(
                self::callback(
                    function (UploadParams $uploadParams) use ($company, $requestBody) {
                        return $uploadParams->getDriverName() === 'api-upload'
                            && $uploadParams->getCompany() === $company
                            && $uploadParams->getUser() === null
                            && $uploadParams->getOptions() === $requestBody;
                    }
                )
            );

        $handler = new CallHandler(
            $featuresAvailableChecker,
            $dataCollector,
            $uploadService,
            $callAnalysisService,
            $company,
            $requestId
        );

        $this->assertTrue($handler->run($requestBody));
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testRunWhenAnalysisNotAvailable(): void
    {
        $roboTruckUploadEventName = 'call_analysis_not_available';

        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $requestId = $this->faker->uuid;
        $requestBody = ['some_key' => 'some value'];

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isCallAnalysisAvailable')
            ->with($company)
            ->willReturn(false);

        $roboTruckEventExtra = [
            'id' => $requestId,
            'company_id' => $companyId,
            'call_id' => null,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckUploadEventName, json_encode($requestBody), $roboTruckEventExtra);

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService->expects($this->never())->method('addToQueue');

        $uploadService = $this->createMock(UploadService::class);
        $uploadService->expects($this->never())->method('uploadCall');

        $handler = new CallHandler(
            $featuresAvailableChecker, $dataCollector, $uploadService, $callAnalysisService, $company, $requestId
        );

        $this->assertFalse($handler->run($requestBody));
    }
}
