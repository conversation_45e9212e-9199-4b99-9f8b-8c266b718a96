<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\ClientApi\Handler;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCall\Daemon\ClientApi\Handler\ChatCallHandler;
use ST<PERSON>all\Entity\Call;
use STCall\Service\CallAnalysis\TranslationStep;
use STCall\Service\CallAnalysisService;
use STCall\Service\Import\CallSaving\Result\Result;
use STCall\Service\Import\UploadParams\UploadParams;
use STCall\Service\Import\UploadService;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Entity\Company;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class ChatCallHandlerTest extends TestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testRun(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $call = $this->createMock(Call::class);

        $requestId = $this->faker->uuid;
        $requestBody = ['some_key' => 'some value'];

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isChatAnalysisAvailable')
            ->with($company)
            ->willReturn(true);

        $dataCollector = $this->createMock(DataCollector::class);

        $result = $this->createMock(Result::class);
        $result->method('isUpdate')->willReturn(true);
        $result->method('getCall')->willReturn($call);

        $uploadService = $this->createMock(UploadService::class);
        $uploadService
            ->expects($this->once())
            ->method('uploadCall')
            ->with(
                self::callback(
                    function (UploadParams $uploadParams) use ($company, $requestBody) {
                        return $uploadParams->getDriverName() === 'chat-api-upload'
                            && $uploadParams->getCompany() === $company
                            && $uploadParams->getOptions() === $requestBody;
                    }
                )
            )
            ->willReturn($result);

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(
                $companyId,
                $call,
                TranslationStep::CALL_TRANSLATION_QUEUE,
                '',
                [
                    'request_id' => $requestId,
                ]
            );

        $handler = new ChatCallHandler(
            $featuresAvailableChecker,
            $dataCollector,
            $uploadService,
            $callAnalysisService,
            $company,
            $requestId
        );

        $this->assertTrue($handler->run($requestBody));
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testRunWhenAnalysisNotAvailable(): void
    {
        $roboTruckUploadEventName = 'call_analysis_not_available';

        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $requestId = $this->faker->uuid;
        $requestBody = ['some_key' => 'some value'];

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isChatAnalysisAvailable')
            ->with($company)
            ->willReturn(false);

        $roboTruckEventExtra = [
            'id' => $requestId,
            'company_id' => $companyId,
            'call_id' => null,
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckUploadEventName, json_encode($requestBody), $roboTruckEventExtra);

        $uploadService = $this->createMock(UploadService::class);
        $uploadService->expects($this->never())->method('uploadCall');

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService->expects($this->never())->method('addToQueue');

        $handler = new ChatCallHandler(
            $featuresAvailableChecker,
            $dataCollector,
            $uploadService,
            $callAnalysisService,
            $company,
            $requestId
        );

        $this->assertFalse($handler->run($requestBody));
    }
}
