<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Export;

use Aws\CommandInterface;
use Aws\Result;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\UriInterface;
use RuntimeException;
use STCall\Service\Export\AwsSdkFactory;
use STCall\Service\Export\ExternalFileService;
use tests\TestCase;

final class ExternalFileServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testSaveFileContent(): void
    {
        $bucket = $this->faker->word();
        $region = $this->faker->text(15);
        $content = $this->faker->text();
        $fileName = $this->faker->word();

        $result = new Result();
        $result->offsetSet('@metadata', ['statusCode' => 200]);

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->expects($this->once())
            ->method('putObject')
            ->with([
                'Bucket' => $bucket,
                'Body' => $content,
                'Key' => $fileName,
            ])
            ->willReturn($result);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($region)
            ->willReturn($awsSdk);

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $externalFileService->saveFileContent($fileName, $content, $bucket, $region);
    }

    /**
     * @throws Exception
     */
    public function testSaveFileContentWhenNoRegion(): void
    {
        $defaultRegion = 'eu-west-3';

        $bucket = $this->faker->word();
        $content = $this->faker->text();
        $fileName = $this->faker->word();

        $result = new Result();
        $result->offsetSet('@metadata', ['statusCode' => 200]);

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->expects($this->once())
            ->method('putObject')
            ->with([
                'Bucket' => $bucket,
                'Body' => $content,
                'Key' => $fileName,
            ])
            ->willReturn($result);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($defaultRegion)
            ->willReturn($awsSdk);

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $externalFileService->saveFileContent($fileName, $content, $bucket);
    }

    /**
     * @throws Exception
     */
    public function testSaveFileContentWhenUnsuccessfulResponse(): void
    {
        $bucket = $this->faker->word();
        $region = $this->faker->text(15);
        $content = $this->faker->text();
        $fileName = $this->faker->word();

        $result = new Result();
        $result->offsetSet('@metadata', ['statusCode' => 500]);

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->expects($this->once())
            ->method('putObject')
            ->with([
                'Bucket' => $bucket,
                'Body' => $content,
                'Key' => $fileName,
            ])
            ->willReturn($result);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($region)
            ->willReturn($awsSdk);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('File upload to s3 was failed');

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $externalFileService->saveFileContent($fileName, $content, $bucket, $region);
    }

    /**
     * @throws Exception
     */
    public function testGetTemporaryUrl(): void
    {
        $bucket = $this->faker->word();
        $region = $this->faker->text(15);

        $fileName = $this->faker->word();
        $timeInMinutes = 360;

        $temporaryUrl = $this->faker->url();

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->method('doesObjectExist')
            ->with($bucket, $fileName)
            ->willReturn(true);

        $getObjectCommand = $this->createMock(CommandInterface::class);
        $awsS3Client
            ->method('getCommand')
            ->with('GetObject', ['Bucket' => $bucket, 'Key' => $fileName])
            ->willReturn($getObjectCommand);

        $uriInterface = $this->createMock(UriInterface::class);
        $uriInterface->method('__toString')->willReturn($temporaryUrl);

        $request = $this->createMock(RequestInterface::class);
        $request->method('getUri')->willReturn($uriInterface);

        $awsS3Client
            ->method('createPresignedRequest')
            ->with($getObjectCommand, '+' . $timeInMinutes . ' minutes')
            ->willReturn($request);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($region)
            ->willReturn($awsSdk);

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $this->assertSame(
            $temporaryUrl,
            $externalFileService->getTemporaryUrl($fileName, $bucket, $region, $timeInMinutes)
        );
    }

    /**
     * @throws Exception
     */
    public function testGetTemporaryUrlWithOnlyRequiredParams(): void
    {
        $bucket = $this->faker->word();
        $defaultRegion = 'eu-west-3';
        $defaultTimeInMinutes = 60;

        $fileName = $this->faker->word();

        $temporaryUrl = $this->faker->url();

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->method('doesObjectExist')
            ->with($bucket, $fileName)
            ->willReturn(true);

        $getObjectCommand = $this->createMock(CommandInterface::class);
        $awsS3Client
            ->method('getCommand')
            ->with('GetObject', ['Bucket' => $bucket, 'Key' => $fileName])
            ->willReturn($getObjectCommand);

        $uriInterface = $this->createMock(UriInterface::class);
        $uriInterface->method('__toString')->willReturn($temporaryUrl);

        $request = $this->createMock(RequestInterface::class);
        $request->method('getUri')->willReturn($uriInterface);

        $awsS3Client
            ->method('createPresignedRequest')
            ->with($getObjectCommand, '+' . $defaultTimeInMinutes . ' minutes')
            ->willReturn($request);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($defaultRegion)
            ->willReturn($awsSdk);

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $this->assertSame(
            $temporaryUrl,
            $externalFileService->getTemporaryUrl($fileName, $bucket)
        );
    }

    /**
     * @throws Exception
     */
    public function testGetTemporaryUrlWhenFileDoesntExist(): void
    {
        $bucket = $this->faker->word();
        $region = $this->faker->text(15);

        $fileName = $this->faker->word();
        $timeInMinutes = 360;

        $awsS3Client = $this->createMock(S3ClientTestStub::class);
        $awsS3Client
            ->method('doesObjectExist')
            ->with($bucket, $fileName)
            ->willReturn(false);

        $awsSdk = $this->createMock(AwsSdkTestStub::class);
        $awsSdk->method('createS3')->willReturn($awsS3Client);

        $awsSdkFactory = $this->createMock(AwsSdkFactory::class);
        $awsSdkFactory
            ->method('create')
            ->with($region)
            ->willReturn($awsSdk);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Object is not exists');

        $externalFileService = new ExternalFileService($awsSdkFactory);
        $externalFileService->getTemporaryUrl($fileName, $bucket, $region, $timeInMinutes);
    }
}
