<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Services\ParagraphsWebhookService;
use tests\TestCase;

class ParagraphsWebhookServiceTest extends TestCase
{
    public function testGetType(): void
    {
        $service = new ParagraphsWebhookService();
        $this->assertSame('paragraphs', $service->getType());
    }

    /**
     * @return void
     */
    public function testFilterData(): void
    {
        $data = [
            [
                'company_id' => 68,
                'call_id' => 'e42a9e98-ab51-4479-b496-18b4b201be07',
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 10,
                'speaker_number' => 0,
                'start_time' => 31.926188,
                'end_time' => 34.26619,
                'timestamp' => null,
                'text' => 'How big is Los Angeles?',
                'en_text' => null,
                'created' => '2025-04-08T11 => 36 => 31.665583Z',
                'event_happenings' => null,
                'speaker_role' => 'unclear'
            ],
            [
                'company_id' => 68,
                'call_id' => 'e42a9e98-ab51-4479-b496-18b4b201be07',
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 11,
                'speaker_number' => 1,
                'start_time' => 34.806187,
                'end_time' => 38.106186,
                'timestamp' => null,
                'text' => 'It has about three million people.',
                'en_text' => null,
                'created' => '2025-04-08T11 => 36 => 31.665689Z',
                'event_happenings' => null,
                'speaker_role' => 'unclear'
            ],
        ];

        $filteredData = [
            [
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 10,
                'speaker_number' => 0,
                'start_time' => 31.926188,
                'end_time' => 34.26619,
                'timestamp' => null,
                'text' => 'How big is Los Angeles?'
            ],
            [
                'call_time' => '2022-12-09T12 => 10 => 10.000000Z',
                'paragraph_number' => 11,
                'speaker_number' => 1,
                'start_time' => 34.806187,
                'end_time' => 38.106186,
                'timestamp' => null,
                'text' => 'It has about three million people.'
            ]
        ];

        $webhookService = new ParagraphsWebhookService();

        $this->assertEquals($filteredData, $webhookService->filterData($data));
    }
}
