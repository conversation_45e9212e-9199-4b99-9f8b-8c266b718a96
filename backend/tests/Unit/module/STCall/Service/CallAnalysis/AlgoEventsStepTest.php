<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Laminas\Db\ResultSet\ResultSet;
use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsTable;
use STCall\Entity\AlgoEvent;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\TranslatorInterface;
use STCall\Service\Precalculation\CallPrecalculationManagerService;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;
use tests\WithConsecutive;

class AlgoEventsStepTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetQueueNames(): void
    {
        $companiesTable = $this->createMock(CompaniesTable::class);
        $hydrator = $this->createMock(Hydrator::class);
        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $nerEventsAlgoApiRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $translator = $this->createMock(TranslatorInterface::class);
        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $step = new AlgoEventsStep(
            $companiesTable,
            $hydrator,
            $callsTable,
            $callFactory,
            $requestParamsBuilder,
            $nerEventsAlgoApiRequestCreator,
            $aiSolutionsCommutatorService,
            $translator,
            $callsAlgoEventsTable,
            $eventTriggerService
        );

        $this->assertSame('call-algo-events-step', $step->getQueueName());
        $this->assertSame('call-algo-events-step-error', $step->getErrorQueueName());
        $this->assertSame('call-mp3-file-convertation-step', $step->getNextStepQueue());
    }

    /**
     * @return void
     * @throws Exception
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws NotFoundApiException
     * @throws ThirdPartyApiException
     * @throws StepIsAlreadyFinishedException
     */
    public function testRun(): void
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $companyId = $this->faker->numberBetween(1, 100);
        $encryptionKey = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getEncryptionKey')->willReturn($encryptionKey);

        $callId = $this->faker->text();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);
        $call
            ->expects($this->exactly(2))
            ->method('isAnalyzed')
            ->with(
                ...
                WithConsecutive::create(
                    [null],
                    [true],
                )
            );
        $call
            ->expects($this->once())
            ->method('setAnalyzedAt')
            ->with($now);

        $companyData = [['key' => 'value']];
        $companiesData = [$companyData];

        $companyResultSet = new ResultSet();
        $companyResultSet->initialize($companiesData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($companyResultSet);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($companyData, Company::class)
            ->willReturn($company);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable->expects($this->once())->method('saveCall')->with($call);

        $callFactory = $this->createMock(CallFactory::class);

        $request1 = $this->createMock(EventsAlgoApiRequestInterface::class);
        $request2 = $this->createMock(EventsAlgoApiRequestInterface::class);

        $requests = [$request1, $request2];

        $requestParams = $this->createMock(RequestParams::class);
        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $nerRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $nerRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn($requests);

        $eventData1 = [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()];
        $eventData2 = [
            $this->faker->word() => $this->faker->sentence(),
            $this->faker->word() => $this->faker->word()
        ];

        $event1 = $this->createMock(AlgoEvent::class);
        $event2 = $this->createMock(AlgoEvent::class);

        $event1->method('toArray')->willReturn($eventData1);
        $event2->method('toArray')->willReturn($eventData2);

        $algoEventCollection = new AlgoEventCollection([$event1, $event2]);

        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $aiSolutionsCommutatorService
            ->method('getAlgoEventsFromCall')
            ->with($company, $call, $requests)
            ->willReturn($algoEventCollection);

        $translator = $this->createMock(TranslatorInterface::class);

        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $callsAlgoEventsTable
            ->expects($this->once())
            ->method('saveEvents')
            ->with($algoEventCollection, $encryptionKey);

        $callPrecalculationManagerService = $this->createMock(CallPrecalculationManagerService::class);
        $callPrecalculationManagerService
            ->expects($this->once())
            ->method('addCallsToPrecalculateQueue')
            ->with($companyId, $callId, CallPrecalculationManagerService::PRECALCULATE_NEW_CALL_PRIORITY);

        $eventsData = [$eventData1, $eventData2];

        $eventName = 'call-algo-events-step-finished';
        $eventParams = [
            'queue_name' => 'call-algo-events-step',
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $eventsData
        ];

        $eventTriggerService = $this->createMock(EventTriggerService::class);
        $eventTriggerService
            ->expects($this->once())
            ->method('trigger')
            ->with($eventName, $eventParams);

        $step = $this->getMockBuilder(AlgoEventsStep::class)
            ->setConstructorArgs([
                'companiesTable' => $companiesTable,
                'hydrator' => $hydrator,
                'callsTable' => $callsTable,
                'callFactory' => $callFactory,
                'requestParamsBuilder' => $requestParamsBuilder,
                'nerRequestCreator' => $nerRequestCreator,
                'aiSolutionsCommutatorService' => $aiSolutionsCommutatorService,
                'translator' => $translator,
                'callsAlgoEventsTable' => $callsAlgoEventsTable,
                'eventTrigger' => $eventTriggerService,
            ])
            ->onlyMethods(['getCall', 'translateAlgoEvents', 'getCallPrecalculationManager'])
            ->getMock();
        $step->method('getCall')->willReturn($call);
        $step
            ->expects($this->once())
            ->method('translateAlgoEvents')
            ->with($algoEventCollection);
        $step
            ->method('getCallPrecalculationManager')
            ->willReturn($callPrecalculationManagerService);

        $step->run($companyId);
    }
}
