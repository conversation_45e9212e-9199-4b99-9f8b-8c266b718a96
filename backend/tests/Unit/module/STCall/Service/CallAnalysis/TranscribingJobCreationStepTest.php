<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use Laminas\Db\ResultSet\ResultSetInterface;
use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCall\Service\CallAnalysis\TranscribingDriver\TwoStepsDriverInterface;
use STCall\Service\CallAnalysis\TranscribingJobCreationStep;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCompany\Data\CompaniesTable;
use STCompany\Entity\Company;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class TranscribingJobCreationStepTest extends TestCase
{
    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testRun(): void
    {
        $roboTruckEventName = 'call_analyze_transcribing_job_creation_step_success';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $call = $this->createMock(Call::class);
        $call
            ->method('getId')
            ->willReturn($callId);
        $call
            ->method('getCompanyId')
            ->willReturn($companyId);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory
            ->method('createCall')
            ->willReturn($call);

        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $companyData = ['some company data'];
        $resultSet = $this->createMock(ResultSetInterface::class);
        $resultSet
            ->method('current')
            ->willReturn($companyData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($resultSet);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($companyData, Company::class)
            ->willReturn($company);

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isCallAnalysisAvailableForCall')
            ->with($call, $company)
            ->willReturn(true);

        $jobName = $this->faker->text(15);
        $driver = $this->createMock(TwoStepsDriverInterface::class);
        $driver
            ->method('createJob')
            ->willReturn($jobName);

        $driverProvider = $this->createMock(DriverProvider::class);
        $driverProvider
            ->method('provide')
            ->willReturn($driver);

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $companyVocabularyService = $this->createMock(CompanyVocabularyService::class);

        $roboTruckEventMessage = 'Job name is ' . $jobName;
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $step = new TranscribingJobCreationStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $featuresAvailableChecker,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $dataCollector,
            [],
            [],
            [],
            [],
            [],
            []
        );
        $step->applyOptions([
            'driver' => 'aws'
        ]);

        $step->launch($callId, $companyId);
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testRunWhenNoAnalysisAvailable(): void
    {
        $roboTruckEventName = 'call_analyze_transcribing_job_creation_step_success';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $call = $this->createMock(Call::class);
        $call
            ->method('getId')
            ->willReturn($callId);
        $call
            ->method('getCompanyId')
            ->willReturn($companyId);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory
            ->method('createCall')
            ->willReturn($call);

        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $companyData = ['some company data'];
        $resultSet = $this->createMock(ResultSetInterface::class);
        $resultSet
            ->method('current')
            ->willReturn($companyData);

        $companiesTable = $this->createMock(CompaniesTable::class);
        $companiesTable
            ->method('getCompany')
            ->with($companyId)
            ->willReturn($resultSet);

        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->with($companyData, Company::class)
            ->willReturn($company);

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isCallAnalysisAvailableForCall')
            ->with($call, $company)
            ->willReturn(false);

        $driverProvider = $this->createMock(DriverProvider::class);
        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $companyVocabularyService = $this->createMock(CompanyVocabularyService::class);
        $dataCollector = $this->createMock(DataCollector::class);

        $step = new TranscribingJobCreationStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $featuresAvailableChecker,
            $driverProvider,
            $callsParagraphsTable,
            $companyVocabularyService,
            $dataCollector,
            [],
            [],
            [],
            [],
            [],
            []
        );
        $step->applyOptions([
            'driver' => 'aws'
        ]);

        $this->expectException(TooLowBalanceForAnalyzeException::class);
        $this->expectExceptionMessage('Company does not have enough time to transcribe this call');

        $step->launch($callId, $companyId);
    }
}
