<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\DTO\AlgoApiChecklistResponse;
use STAlgo\Service\AiSolutionsCommutatorService;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\Checklist\ChecklistRunChecker;
use STCall\Service\CallAnalysis\ChecklistStep;
use STCall\Service\CallChecklistService;
use STCall\Service\CallService;
use STCall\Service\EventTriggerService;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Checklist\ChecklistCollection;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Entity\Company;
use STCompany\Service\Checklist\ChecklistPointService;
use STCompany\Service\Checklist\ChecklistService;
use STCompany\Service\CompanyService;
use tests\TestCase;

final class ChecklistStepTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunch(): void
    {
        $callId = $this->faker->text();
        $companyId = $this->faker->numberBetween(1, 100);

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);

        $callService = $this->createMock(CallService::class);
        $callsTable = $this->createMock(CallsTable::class);
        $callChecklistService = $this->createMock(CallChecklistService::class);

        $checklistId1 = $this->faker->numberBetween(1, 100);
        $checklistId2 = $this->faker->numberBetween(101, 200);

        $checklist1 = $this->createMock(Checklist::class);
        $checklist2 = $this->createMock(Checklist::class);

        $checklist1->method('getId')->willReturn($checklistId1);
        $checklist2->method('getId')->willReturn($checklistId2);

        $checklistData1 = [$this->faker->word(), $this->faker->word()];
        $checklistData2 = [$this->faker->word(), $this->faker->word()];

        $checklist1->method('toArray')->willReturn($checklistData1);
        $checklist2->method('toArray')->willReturn($checklistData2);

        $checklistCollection = new ChecklistCollection([$checklist1, $checklist2]);

        $checklistService = $this->createMock(ChecklistService::class);
        $checklistService
            ->method('getCompanyChecklists')
            ->with($companyId)
            ->willReturn($checklistCollection);

        $checklistPoint1 = $this->createMock(ChecklistPoint::class);
        $checklistPoint2 = $this->createMock(ChecklistPoint::class);
        $checklistPoint3 = $this->createMock(ChecklistPoint::class);

        $checklistPointData1 = [$this->faker->word(), $this->faker->word()];
        $checklistPointData2 = [$this->faker->word(), $this->faker->word()];
        $checklistPointData3 = [$this->faker->word(), $this->faker->word()];

        $checklistPoint1->method('toArray')->willReturn($checklistPointData1);
        $checklistPoint2->method('toArray')->willReturn($checklistPointData2);
        $checklistPoint3->method('toArray')->willReturn($checklistPointData3);

        $checklistPointCollection1 = new ChecklistPointCollection([$checklistPoint1, $checklistPoint2]);
        $checklistPointCollection2 = new ChecklistPointCollection([$checklistPoint3]);

        $checklistPointServiceMap = [
            [$checklistId1, $companyId, $checklistPointCollection1],
            [$checklistId2, $companyId, $checklistPointCollection2],
        ];

        $checklistPointService = $this->createMock(ChecklistPointService::class);
        $checklistPointService
            ->method('getChecklistPointsByChecklistId')
            ->willReturnMap($checklistPointServiceMap);

        $callFactory = $this->createMock(CallFactory::class);
        $callFactory->method('createCall')->willReturn($call);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('isChecklistsEnabled')->willReturn(true);

        $companyService = $this->createMock(CompanyService::class);
        $companyService->method('getCompany')->willReturn($company);

        $checklistRunChecker = $this->createMock(ChecklistRunChecker::class);
        $checklistRunChecker->method('shouldRunChecklist')->willReturn(true);

        $algoApiChecklistResponseData1 = [$this->faker->word(), $this->faker->word()];
        $algoApiChecklistResponse1 = $this->createMock(AlgoApiChecklistResponse::class);
        $algoApiChecklistResponse1->method('getChecklistResult')->willReturn($algoApiChecklistResponseData1);
        $algoApiChecklistResponse1->method('isSuccessful')->willReturn(true);

        $algoApiChecklistResponseData2 = [$this->faker->word(), $this->faker->word()];
        $algoApiChecklistResponse2 = $this->createMock(AlgoApiChecklistResponse::class);
        $algoApiChecklistResponse2->method('getChecklistResult')->willReturn($algoApiChecklistResponseData2);
        $algoApiChecklistResponse2->method('isSuccessful')->willReturn(true);

        $aiSolutionsCommutatorService = $this->createMock(AiSolutionsCommutatorService::class);
        $aiSolutionsCommutatorService
            ->method('getChecklistResult')
            ->willReturnOnConsecutiveCalls(
                $algoApiChecklistResponse1,
                $algoApiChecklistResponse2
            );

        $data = [
            [
                'checklist' => $checklistData1,
                'checklist_points' => [
                    $checklistPointData1,
                    $checklistPointData2,
                ],
                'checklist_result' => $algoApiChecklistResponseData1,
            ],
            [
                'checklist' => $checklistData2,
                'checklist_points' => [$checklistPointData3],
                'checklist_result' => $algoApiChecklistResponseData2,
            ]
        ];

        $eventName = 'call-checklist-step-finished';
        $eventParams = [
            'queue_name' => 'call-checklist-step',
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $data
        ];

        $eventTrigger = $this->createMock(EventTriggerService::class);
        $eventTrigger
            ->expects($this->once())
            ->method('trigger')
            ->with($eventName, $eventParams);

        $step = new ChecklistStep(
            $callService,
            $callsTable,
            $callChecklistService,
            $checklistService,
            $checklistPointService,
            $callFactory,
            $companyService,
            $checklistRunChecker,
            $aiSolutionsCommutatorService,
            $eventTrigger
        );

        $step->launch($callId, $companyId);
    }
}
