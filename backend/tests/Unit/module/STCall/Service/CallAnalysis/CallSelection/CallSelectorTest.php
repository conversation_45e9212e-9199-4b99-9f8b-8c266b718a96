<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis\CallSelection;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\CallSelection\CallSelector;
use tests\TestCase;

final class CallSelectorTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testGetCall(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $callData = ['some call data'];
        $callsTable = $this->createMock(CallsTable::class);
        $callsTable->method('getCall')->with($callId, $companyId)->willReturn($callData);

        $call = $this->createMock(Call::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callFactory->method('createCall')->with($callData)->willReturn($call);

        $selector = new CallSelector($callsTable, $callFactory);
        $this->assertSame($call, $selector->getCall($callId, $companyId));
    }
}
