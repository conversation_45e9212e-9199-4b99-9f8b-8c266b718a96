<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis\Checklist;

use PHPUnit\Framework\MockObject\Exception;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use STCall\Service\CallAnalysis\Checklist\ChecklistRunChecker;
use STCall\Service\Interfaces\FeaturesAvailableCheckerInterface;
use STCall\Service\Interfaces\TeamSelectorInterface;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Company;
use tests\TestCase;

final class ChecklistRunCheckerTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testShouldRunChecklist(): void
    {
        $call = $this->createMock(Call::class);
        $call->method('getDuration')->willReturn(100);
        $call->method('getCallStatus')->willReturn(CallsTable::CALL_STATUS_RINGING);
        $company = $this->createMock(Company::class);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklist = $this->createMock(Checklist::class);
        $checklist->method('getId')->willReturn($checklistId);
        $checklist->method('getCallDurationThreshold')->willReturn(99);
        $checklist
            ->method('getCallsStatuses')
            ->willReturn([
                CallsTable::CALL_STATUS_ANSWERED,
                CallsTable::CALL_STATUS_RINGING
            ]);
        $checklist->method('getCallsScope')->willReturn(Checklist::CALLS_SCOPE_ALL);

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isChecklistAvailable')
            ->with($checklistId, $company)
            ->willReturn(true);

        $teamSelector = $this->createMock(TeamSelectorInterface::class);
        $callsTable = $this->createMock(CallsTable::class);

        $checklistRunChecker = new ChecklistRunChecker($featuresAvailableChecker, $teamSelector, $callsTable);

        $this->assertTrue($checklistRunChecker->shouldRunChecklist($call, $company, $checklist));
    }

    /**
     * @throws Exception
     */
    public function testShouldRunChecklistWhenChecklistIsNotAvailable(): void
    {
        $call = $this->createMock(Call::class);
        $company = $this->createMock(Company::class);

        $checklistId = $this->faker->numberBetween(1, 100);
        $checklist = $this->createMock(Checklist::class);
        $checklist->method('getId')->willReturn($checklistId);

        $featuresAvailableChecker = $this->createMock(FeaturesAvailableCheckerInterface::class);
        $featuresAvailableChecker
            ->method('isChecklistAvailable')
            ->with($checklistId, $company)
            ->willReturn(false);

        $teamSelector = $this->createMock(TeamSelectorInterface::class);
        $callsTable = $this->createMock(CallsTable::class);

        $checklistRunChecker = new ChecklistRunChecker($featuresAvailableChecker, $teamSelector, $callsTable);

        $this->assertFalse($checklistRunChecker->shouldRunChecklist($call, $company, $checklist));
    }
}
