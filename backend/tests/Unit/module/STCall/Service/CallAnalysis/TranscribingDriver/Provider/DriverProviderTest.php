<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis\TranscribingDriver\Provider;

use PHPUnit\Framework\MockObject\Exception;
use STCall\Entity\Call;
use STCall\Service\CallAnalysis\TranscribingDriver\DeepgramNova3Driver;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyVocabularyCollection;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

final class DriverProviderTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testProvideWhenDeepgramNova3Driver(): void
    {
        $dataCollector = $this->createMock(DataCollector::class);

        $driverConfig = [$this->faker->word() => $this->faker->word()];
        $company = $this->createMock(Company::class);
        $call = $this->createMock(Call::class);
        $companyVocabularyCollection = $this->createMock(CompanyVocabularyCollection::class);

        $provider = new DriverProvider($dataCollector);
        $driver = $provider->provide(
            'deepgramNova3',
            ['api' => [], 'env' => ''],
            $driverConfig,
            $company,
            $call,
            $companyVocabularyCollection
        );

        $this->assertInstanceOf(DeepgramNova3Driver::class, $driver);
        $this->assertSame($company, $driver->getCompany());
        $this->assertSame($call, $driver->getCall());
        $this->assertSame($companyVocabularyCollection, $driver->getCompanyVocabulary());
        $this->assertSame($driverConfig, $driver->getDriverConfig());
    }
}
