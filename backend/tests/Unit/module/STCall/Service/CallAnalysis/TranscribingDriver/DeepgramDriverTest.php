<?php

namespace tests\Unit\module\STCall\Service\CallAnalysis\TranscribingDriver;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Entity\Call;
use STCall\Service\CallAnalysis\TranscribingDriver\DeepgramDriver;
use STCompany\Entity\Company;
use stdClass;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;

class DeepgramDriverTest extends TestCase
{
    /**
     * @throws GuzzleException
     * @throws Exception
     * @throws ThirdPartyApiException
     */
    public function testTranscribe(): void
    {
        $roboTruckEventName = 'call_analyze_transcribing_driver';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();

        $company = $this->createMock(Company::class);
        $company
            ->method('getId')
            ->willReturn($companyId);

        $call = $this->createMock(Call::class);
        $call
            ->method('getCallType')
            ->willReturn(Call::CALL_TYPE);
        $call
            ->method('getId')
            ->willReturn($callId);

        $metadata = new stdClass();
        $metadata->request_id = $this->faker->text(15);
        $callResult = new stdClass();
        $callResult->metadata = $metadata;

        $alternative = new stdClass();
        $alternative->paragraphs = [];

        $channel = new stdClass();
        $channel->alternatives = [$alternative];

        $results = new stdClass();
        $results->channels = [$channel];

        $callResult->results = $results;

        $roboTruckEventMessage = 'deepgram transcribing request';
        $roboTruckEventExtra = [
            'id' => $metadata->request_id,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra, $oldLogName);

        $driver = $this->getMockBuilder(DeepgramDriver::class)
            ->setConstructorArgs(['dataCollector' => $dataCollector])
            ->onlyMethods(['call', 'getFileLink', 'getResponseToCollectionConverter'])
            ->getMock();

        $driver
            ->method('call')
            ->willReturn($callResult);
        $driver->setCall($call);
        $driver->setCompany($company);

        $driver->transcribe();
    }
}
