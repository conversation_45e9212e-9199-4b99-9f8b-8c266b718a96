<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoEvents\RequestCreation;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequestCreator;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STCompany\Entity\Company;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection;
use STCompany\Service\LlmEvent\LlmEventSelectorService;
use STConfiguration\Service\ConfigurationService;
use tests\TestCase;

class LlmEventsAlgoApiRequestCreatorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testCreateWhenLlmEventsExist(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $algoApiUrl = trim($this->faker->url(), '/');
        $configuration = $this->createMock(ConfigurationService::class);
        $configuration
            ->method('get')
            ->with('algo')
            ->willReturn(['algo_api_llm_detection_url' => $algoApiUrl]);

        $name1 = $this->faker->word();
        $name2 = $this->faker->word();
        $description1 = $this->faker->text();
        $description2 = $this->faker->text();

        $llmEvent1 = new LlmEvent();
        $llmEvent1->setName($name1);
        $llmEvent1->setDescription($description1);

        $llmEvent2 = new LlmEvent();
        $llmEvent2->setName($name2);
        $llmEvent2->setDescription($description2);

        $llmEvents = [$llmEvent1, $llmEvent2];

        $llmEventsCollection = new LlmEventCollection($llmEvents);

        $companyLlmEventSelector = $this->createMock(LlmEventSelectorService::class);
        $companyLlmEventSelector
            ->method('getLlmEvents')
            ->with($companyId)
            ->willReturn($llmEventsCollection);

        $llmEventsParams = [
            [
                'title' => $name1,
                'description' => $description1,
            ],
            [
                'title' => $name2,
                'description' => $description2,
            ],
        ];

        $requestParams = $this->createMock(RequestParams::class);
        $requestParams
            ->expects(self::once())
            ->method('addParams')
            ->with('events', $llmEventsParams);

        $expectedUrl = $algoApiUrl . '/api/algo/promptV4';
        $requestParamsData = ['some key' => 'some value'];
        $requestParams
            ->method('toArray')
            ->willReturn($requestParamsData);

        $creator = new LlmEventsAlgoApiRequestCreator($companyLlmEventSelector, $configuration);
        $request = $creator->create($company, $requestParams);

        $this->assertInstanceOf(LlmEventsAlgoApiRequest::class, $request);
        $this->assertSame($expectedUrl, $request->getUrl());
        $this->assertSame($requestParamsData, $request->getParams());
    }

    /**
     * @throws Exception
     */
    public function testCreateWhenNoLlmEvents(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $configuration = $this->createMock(ConfigurationService::class);

        $llmEventsCollection = new LlmEventCollection([]);

        $companyLlmEventSelector = $this->createMock(LlmEventSelectorService::class);
        $companyLlmEventSelector
            ->method('getLlmEvents')
            ->with($companyId)
            ->willReturn($llmEventsCollection);

        $requestParams = $this->createMock(RequestParams::class);

        $creator = new LlmEventsAlgoApiRequestCreator($companyLlmEventSelector, $configuration);
        $this->assertNull($creator->create($company, $requestParams));
    }
}
