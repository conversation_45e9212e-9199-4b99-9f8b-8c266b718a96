<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoEvents\RequestCreation;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\AlgoEvents\RequestCreation\ResponseToAlgoEventConverter;
use STCall\Entity\AlgoEvent;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\Hydrator;
use tests\TestCase;

class ResponseToAlgoEventConverterTest extends TestCase
{
    /**
     * @throws Exception
     * @throws \ReflectionException
     */
    public function testConvert(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $time = Carbon::now();
        $callId = $this->faker->text();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getTime')->willReturn($time);

        $algoApiId = $this->faker->numberBetween(101, 200);
        $industryId = $this->faker->numberBetween(201, 300);

        $request = $this->createMock(EventsAlgoApiRequestInterface::class);
        $request->method('getAlgoApiId')->willReturn($algoApiId);
        $request->method('getIndustryId')->willReturn($industryId);

        $algoEventCollection = new AlgoEventCollection();

        $segment1 = new \stdClass();
        $segment1->class = 'class1';
        $segment1->id = $this->faker->numberBetween(301, 400);
        $segment1->score = $this->faker->numberBetween(1, 100) / 100;
        $segment1->sentence = $this->faker->text();

        $segment2 = new \stdClass();
        $segment2->class = 'class2';
        $segment2->id = $this->faker->numberBetween(401, 500);
        $segment2->score = $this->faker->numberBetween(1, 100) / 100;
        $segment2->sentence = $this->faker->text();

        $segments = [$segment1, $segment2];

        $results = new \stdClass();
        $results->segments = $segments;

        $eventData = new \stdClass();
        $eventData->results = $results;
        $eventData->status = 'ok';

        $algoEvent1 = $this->createMock(AlgoEvent::class);
        $algoEvent1->method('getAlgoApiId')->willReturn($algoApiId);
        $algoEvent1->method('getCallId')->willReturn($callId);
        $algoEvent1->method('getParagraphNumber')->willReturn($segment1->id);
        $algoEvent1->method('getEvent')->willReturn($segment1->class);

        $algoEvent2 = $this->createMock(AlgoEvent::class);
        $algoEvent2->method('getAlgoApiId')->willReturn($algoApiId);
        $algoEvent2->method('getCallId')->willReturn($callId);
        $algoEvent2->method('getParagraphNumber')->willReturn($segment2->id);
        $algoEvent2->method('getEvent')->willReturn($segment2->class);

        $jsonResponse = json_encode($eventData);

        $hydratorMap = [
            [
                [
                    'company_id' => $companyId,
                    'call_id' => $callId,
                    'paragraph_number' => $segment1->id,
                    'algo_api_id' => $algoApiId,
                    'industry_id' => $industryId,
                    'event' => $segment1->class,
                    'call_time' => $time,
                    'score' => $segment1->score,
                    'main_point_phrase' => $segment1->sentence,
                    'main_point_location' => [],
                ],
                AlgoEvent::class,
                $algoEvent1
            ],
            [
                [
                    'company_id' => $companyId,
                    'call_id' => $callId,
                    'paragraph_number' => $segment2->id,
                    'algo_api_id' => $algoApiId,
                    'industry_id' => $industryId,
                    'event' => $segment2->class,
                    'call_time' => $time,
                    'score' => $segment2->score,
                    'main_point_phrase' => $segment2->sentence,
                    'main_point_location' => [],
                ],
                AlgoEvent::class,
                $algoEvent2,
            ]
        ];
        $hydrator = $this->createMock(Hydrator::class);
        $hydrator
            ->method('hydrateClass')
            ->willReturnMap($hydratorMap);

        $converter = new ResponseToAlgoEventConverter($hydrator);
        $converter->convert($algoEventCollection, $jsonResponse, $company, $call, $request);

        $algoEventCollection->rewind();
        $this->assertSame($algoEvent1, $algoEventCollection->current());
        $algoEventCollection->next();
        $this->assertSame($algoEvent2, $algoEventCollection->current());
    }
}
