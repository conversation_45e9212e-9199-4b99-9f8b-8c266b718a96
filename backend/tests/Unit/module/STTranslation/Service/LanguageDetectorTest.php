<?php

declare(strict_types=1);

namespace tests\Unit\module\STTranslation\Service;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\Interfaces\AlgoClientInterface;
use STTranslation\Service\Interfaces\ConfigurationInterface;
use STTranslation\Service\LanguageDetector;
use STTranslation\Service\Whisper\Sdk;
use tests\TestCase;

final class LanguageDetectorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testSetInvalidProvider(): void
    {
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => 'invalid_provider']);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid language detection provider: invalid_provider. Valid providers are: whisper, algo_api');
        
        new LanguageDetector($whisperSdk, $algoClient, $configuration);
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByTextWithEmptyText(): void
    {
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_WHISPER]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage('Cannot detect language from empty text');
        
        $languageDetector->detectLanguageByText('');
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByContentWithEmptyContent(): void
    {
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_WHISPER]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage('Cannot detect language from empty content');
        
        $languageDetector->detectLanguageByContent('');
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByTextWithWhisperProvider(): void
    {
        $text = $this->faker->text();
        $languageCode = $this->faker->languageCode();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $whisperSdk->method('detectLanguageByText')
            ->with($text)
            ->willReturn($languageCode);
            
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_WHISPER]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->assertSame($languageCode, $languageDetector->detectLanguageByText($text));
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByTextWithAlgoApiProvider(): void
    {
        $text = $this->faker->text();
        $languageCode = $this->faker->languageCode();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient->method('detectLanguageByText')
            ->with($text)
            ->willReturn(['results' => ['language' => $languageCode]]);
            
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_ALGO_API]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->assertSame($languageCode, $languageDetector->detectLanguageByText($text));
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByTextWithAlgoApiProviderInvalidResponse(): void
    {
        $text = $this->faker->text();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient->method('detectLanguageByText')
            ->with($text)
            ->willReturn(['invalid' => 'response']);
            
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_ALGO_API]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage('Failed to detect language using algo API');
        
        $languageDetector->detectLanguageByText($text);
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByContentWithWhisperProvider(): void
    {
        $content = $this->faker->text();
        $languageCode = $this->faker->languageCode();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $whisperSdk->method('detectLanguage')
            ->with($content)
            ->willReturn($languageCode);
            
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_WHISPER]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->assertSame($languageCode, $languageDetector->detectLanguageByContent($content));
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByContentWithAlgoApiProvider(): void
    {
        $content = $this->faker->text();
        $languageCode = $this->faker->languageCode();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient->method('detectLanguage')
            ->with($content)
            ->willReturn(['language' => $languageCode]);
            
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_ALGO_API]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->assertSame($languageCode, $languageDetector->detectLanguageByContent($content));
    }
    
    /**
     * @throws Exception
     */
    public function testDetectLanguageByContentWithAlgoApiProviderInvalidResponse(): void
    {
        $content = $this->faker->text();
        
        $whisperSdk = $this->createMock(Sdk::class);
        $algoClient = $this->createMock(AlgoClientInterface::class);
        $algoClient->method('detectLanguage')
            ->with($content)
            ->willReturn(['invalid' => 'response']);
            
        $configuration = $this->createMock(ConfigurationInterface::class);
        
        $configuration->method('get')
            ->with('translation')
            ->willReturn(['language_detection_provider' => LanguageDetector::PROVIDER_ALGO_API]);
        
        $languageDetector = new LanguageDetector($whisperSdk, $algoClient, $configuration);
        
        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage('Failed to detect language using algo API');
        
        $languageDetector->detectLanguageByContent($content);
    }
}
