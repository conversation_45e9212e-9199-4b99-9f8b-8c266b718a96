<?php

namespace tests\Unit\module\STRoboMetrics\Service;

use PHPUnit\Framework\TestCase;
use STRoboMetrics\Service\ChecklistFormatterService;

class ChecklistFormatterServiceTest extends TestCase
{
    private ChecklistFormatterService $service;

    protected function setUp(): void
    {
        $this->service = new ChecklistFormatterService();
    }

    public function testFormatChecklistsStatistics(): void
    {
        $checklistsStats = [
            [
                'company_id' => 1,
                'company_name' => 'Company 1',
                'checklists' => [
                    [
                        'checklist_name' => 'Checklist 1',
                        'total_checklist_conversations' => 10,
                        'billing_units' => 1
                    ],
                    [
                        'checklist_name' => 'Checklist 2',
                        'total_checklist_conversations' => 20,
                        'billing_units' => 2
                    ],
                ]
            ],
        ];

        $result = $this->service->formatChecklistsStatistics($checklistsStats);

        $this->assertEquals(3, $result[0]['billing_units']);
        $this->assertEquals(<<<EOT
            Checklist 1: 10
            Checklist 2: 20\n
            EOT,
            $result[0]['checklists']
        );
    }

    public function testFormatChecklistsStatisticsWithEmptyInput(): void
    {
        $result = $this->service->formatChecklistsStatistics([]);

        $this->assertEquals(
            [],
            $result
        );
    }

    public function testFormatCombinedUsageStatistics(): void
    {
        // Test data
        $callsStats = [
            [
                'company_name' => 'Company A',
                'total_call_hours' => 10.5
            ],
            [
                'company_name' => 'Company B',
                'total_call_hours' => 5.2
            ]
        ];

        $chatStats = [
            [
                'company_name' => 'Company A',
                'total_chats_count' => 100
            ],
            [
                'company_name' => 'Company C',
                'total_chats_count' => 50
            ]
        ];

        $checklistsStats = [
            [
                'company_name' => 'Company A',
                'billing_units' => 3,
                'checklists' => "Checklist 1: 10\nChecklist 2: 20\n"
            ],
            [
                'company_name' => 'Company B',
                'billing_units' => 1,
                'checklists' => "Checklist 3: 15\n"
            ]
        ];

        $summarizationStats = [
            [
                'company_name' => 'Company A',
                'total_summarization_conversations' => 75
            ],
            [
                'company_name' => 'Company D',
                'total_summarization_conversations' => 30
            ]
        ];

        // Call the method
        $result = $this->service->formatUsageStatistics(
            $callsStats,
            $chatStats,
            $checklistsStats,
            $summarizationStats
        );

        // Create a map of results by company name for easier testing
        $resultMap = [];
        foreach ($result as $stat) {
            $resultMap[$stat['company_name']] = $stat;
        }

        // Check that all companies are included
        $this->assertCount(4, $result);
        $this->assertArrayHasKey('Company A', $resultMap);
        $this->assertArrayHasKey('Company B', $resultMap);
        $this->assertArrayHasKey('Company C', $resultMap);
        $this->assertArrayHasKey('Company D', $resultMap);

        // Check Company A (has data in all statistics)
        $this->assertEquals(10.5, $resultMap['Company A']['total_call_hours']);
        $this->assertEquals(100, $resultMap['Company A']['total_chats_count']);
        $this->assertEquals(75, $resultMap['Company A']['total_summarization_conversations']);
        $this->assertEquals("Checklist 1: 10\nChecklist 2: 20\n", $resultMap['Company A']['checklists']);
        $this->assertEquals(3, $resultMap['Company A']['billing_units']);

        // Check Company B (missing chat and summarization data)
        $this->assertEquals(5.2, $resultMap['Company B']['total_call_hours']);
        $this->assertEquals(0, $resultMap['Company B']['total_chats_count']);
        $this->assertEquals(0, $resultMap['Company B']['total_summarization_conversations']);
        $this->assertEquals("Checklist 3: 15\n", $resultMap['Company B']['checklists']);
        $this->assertEquals(1, $resultMap['Company B']['billing_units']);

        // Check Company C (only has chat data)
        $this->assertEquals(0, $resultMap['Company C']['total_call_hours']);
        $this->assertEquals(50, $resultMap['Company C']['total_chats_count']);
        $this->assertEquals(0, $resultMap['Company C']['total_summarization_conversations']);
        $this->assertEquals('', $resultMap['Company C']['checklists']);
        $this->assertEquals(0, $resultMap['Company C']['billing_units']);

        // Check Company D (only has summarization data)
        $this->assertEquals(0, $resultMap['Company D']['total_call_hours']);
        $this->assertEquals(0, $resultMap['Company D']['total_chats_count']);
        $this->assertEquals(30, $resultMap['Company D']['total_summarization_conversations']);
        $this->assertEquals('', $resultMap['Company D']['checklists']);
        $this->assertEquals(0, $resultMap['Company D']['billing_units']);
    }

    public function testFormatCombinedUsageStatisticsWithEmptyInput(): void
    {
        $result = $this->service->formatUsageStatistics([], [], [], []);

        $this->assertEquals([], $result);
    }
}
