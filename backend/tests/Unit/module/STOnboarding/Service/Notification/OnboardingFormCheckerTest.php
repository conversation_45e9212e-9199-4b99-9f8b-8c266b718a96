<?php

declare(strict_types=1);

namespace tests\Unit\module\STOnboarding\Service\Notification;

use PHPUnit\Framework\MockObject\Exception;
use STOnboarding\Entity\OnboardingForm;
use STOnboarding\Service\Notification\OnboardingFormChecker;
use tests\TestCase;

final class OnboardingFormCheckerTest extends TestCase
{
    /**
     * @return void
     */
    public function testGetUnfilledFieldsWhenAllFieldsFilled(): void
    {
        $someIndustryName = $this->faker->word();
        $someIndustryId = $this->faker->numberBetween(1, 100);

        $industries = [
            $someIndustryName => [
                'name' => $someIndustryName,
                'type' => 'new',
                'is_active' => false,
                'events' => ['some industry\'s events'],
            ],
            $someIndustryId => [
                'id' => $someIndustryId,
                'type' => 'existing',
                'is_active' => true,
                'events' => ['some events'],
            ]
        ];

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setExternalId($this->faker->uuid);
        $onboardingForm->setFrontFormLink($this->faker->url());
        $onboardingForm->setInviteLink($this->faker->url());
        $onboardingForm->setIsSubmitted(false);
        $onboardingForm->setCompanyName($this->faker->word());
        $onboardingForm->setCompanyLogo($this->faker->text());
        $onboardingForm->setUsers([$this->faker->word(), $this->faker->text()]);
        $onboardingForm->setIndustries($industries);
        $onboardingForm->setCallsSettings([$this->faker->text(), $this->faker->sentence()]);
        $onboardingForm->setCompanyId(null);

        $checker = new OnboardingFormChecker();

        $this->assertSame([], $checker->getUnfilledFields($onboardingForm));
    }

    /**
     * @return void
     */
    public function testGetUnfilledFieldsWhenNotAllFieldsFilled(): void
    {
        $onboardingForm = new OnboardingForm();
        $onboardingForm->setExternalId($this->faker->uuid);
        $onboardingForm->setFrontFormLink($this->faker->url());
        $onboardingForm->setInviteLink($this->faker->url());
        $onboardingForm->setIsSubmitted(true);
        $onboardingForm->setCompanyName($this->faker->word());
        $onboardingForm->setUsers([$this->faker->word(), $this->faker->text()]);

        $checker = new OnboardingFormChecker();

        $this->assertSame([
            'company_logo' => 'company_logo',
            'industries' => 'industries',
            'calls_settings' => 'calls_settings'
        ], $checker->getUnfilledFields($onboardingForm));
    }

    public function testGetUnfilledFieldsWhenNoEvents(): void
    {
        $someIndustryName = $this->faker->text(15);
        $industries = [
            $someIndustryName => [
                'name' => $someIndustryName,
                'type' => 'new',
                'is_active' => true,
                'events' => [],
            ],
        ];

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setExternalId($this->faker->uuid);
        $onboardingForm->setFrontFormLink($this->faker->url());
        $onboardingForm->setInviteLink($this->faker->url());
        $onboardingForm->setIsSubmitted(true);
        $onboardingForm->setCompanyName($this->faker->word());
        $onboardingForm->setCompanyLogo($this->faker->word());
        $onboardingForm->setUsers([$this->faker->word(), $this->faker->text()]);
        $onboardingForm->setIndustries($industries);
        $onboardingForm->setCallsSettings([$this->faker->text(), $this->faker->sentence()]);

        $checker = new OnboardingFormChecker();

        $this->assertSame(['industries.events' => 'industries.events'], $checker->getUnfilledFields($onboardingForm));
    }

    /**
     * @throws Exception
     */
    public function testIsFormCompletedWhenAllRequiredFieldsFilled(): void
    {
        $onboardingForm = $this->createMock(OnboardingForm::class);

        $unfilledFields = [
            'company_logo' => 'company_logo',
            'calls_settings' => 'calls_settings',
        ];
        $checker = $this->getMockBuilder(OnboardingFormChecker::class)
            ->onlyMethods(['getUnfilledFields'])
            ->disableOriginalConstructor()
            ->getMock();
        $checker
            ->method('getUnfilledFields')
            ->with($onboardingForm)
            ->willReturn($unfilledFields);

        $this->assertSame(true, $checker->isFormCompleted($onboardingForm));
    }

    /**
     * @dataProvider requiredUnfilledFieldsDataProvider
     * @param array $unfilledFields
     * @return void
     * @throws Exception
     */
    public function testIsFormCompletedWhenNotAllRequiredFieldsFilled(array $unfilledFields): void
    {
        $onboardingForm = $this->createMock(OnboardingForm::class);

        $checker = $this->getMockBuilder(OnboardingFormChecker::class)
            ->onlyMethods(['getUnfilledFields'])
            ->disableOriginalConstructor()
            ->getMock();
        $checker
            ->method('getUnfilledFields')
            ->with($onboardingForm)
            ->willReturn($unfilledFields);

        $this->assertSame(false, $checker->isFormCompleted($onboardingForm));
    }

    public static function requiredUnfilledFieldsDataProvider(): array
    {
        return [
            [
                'unfilledFields' => ['company_name' => 'company_name'],
            ],
            [
                'unfilledFields' => ['users' => 'users'],
            ],
            [
                'unfilledFields' => ['industries' => 'industries'],
            ],
            [
                'unfilledFields' => ['industries.events' => 'industries.events'],
            ],
        ];
    }
}
