<?php

declare(strict_types=1);

namespace tests\Unit\module\STOnboarding\Entity;

use STOnboarding\Entity\OnboardingForm;
use tests\TestCase;

final class OnboardingFormTest extends TestCase
{
    public function testGetActiveIndustryEvents(): void
    {
        $someIndustryName = $this->faker->word();
        $someIndustryId = $this->faker->numberBetween(1, 100);

        $eventId = $this->faker->numberBetween(201, 300);

        $industries = [
            $someIndustryName => [
                'name' => $someIndustryName,
                'type' => 'new',
                'is_active' => false,
                'events' => ['some industry\'s events'],
            ],
            $someIndustryId => [
                'id' => $someIndustryId,
                'type' => 'existing',
                'is_active' => true,
                'events' => [
                    [
                        'id' => $eventId,
                        'name' => $this->faker->sentence(2),
                        'description' => $this->faker->text(255),
                    ],
                    [
                        'name' => $this->faker->word(),
                        'description' => $this->faker->sentence(10),
                    ]
                ],
            ]
        ];

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setIndustries($industries);

        $this->assertSame($industries[$someIndustryId]['events'], $onboardingForm->getActiveIndustryEvents());
    }

    public function testGetActiveIndustryEventsWhenEventsEmpty(): void
    {
        $someIndustryName = $this->faker->word();
        $someIndustryId = $this->faker->numberBetween(1, 100);

        $industries = [
            $someIndustryName => [
                'name' => $someIndustryName,
                'type' => 'new',
                'is_active' => false,
                'events' => ['some industry\'s events'],
            ],
            $someIndustryId => [
                'id' => $someIndustryId,
                'type' => 'existing',
                'is_active' => true,
                'events' => [],
            ]
        ];

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setIndustries($industries);

        $this->assertSame([], $onboardingForm->getActiveIndustryEvents());
    }

    public function testGetActiveIndustryEventsWhenIndustriesEmpty(): void
    {
        $onboardingForm = new OnboardingForm();
        $onboardingForm->setIndustries([]);

        $this->assertSame([], $onboardingForm->getActiveIndustryEvents());
    }
}
